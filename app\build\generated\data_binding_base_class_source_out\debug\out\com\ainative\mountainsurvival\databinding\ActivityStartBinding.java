// Generated by view binder compiler. Do not edit!
package com.ainative.mountainsurvival.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ainative.mountainsurvival.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityStartBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout exitGameButton;

  @NonNull
  public final LinearLayout startGameButton;

  private ActivityStartBinding(@NonNull LinearLayout rootView, @NonNull LinearLayout exitGameButton,
      @NonNull LinearLayout startGameButton) {
    this.rootView = rootView;
    this.exitGameButton = exitGameButton;
    this.startGameButton = startGameButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityStartBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityStartBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_start, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityStartBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.exitGameButton;
      LinearLayout exitGameButton = ViewBindings.findChildViewById(rootView, id);
      if (exitGameButton == null) {
        break missingId;
      }

      id = R.id.startGameButton;
      LinearLayout startGameButton = ViewBindings.findChildViewById(rootView, id);
      if (startGameButton == null) {
        break missingId;
      }

      return new ActivityStartBinding((LinearLayout) rootView, exitGameButton, startGameButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
