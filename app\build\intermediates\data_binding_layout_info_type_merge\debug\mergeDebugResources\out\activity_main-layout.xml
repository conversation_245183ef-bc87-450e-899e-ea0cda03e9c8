<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.ainative.mountainsurvival" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="159" endOffset="51"/></Target><Target id="@+id/statusBarLayout" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="73" endOffset="18"/></Target><Target id="@+id/warmthTextView" view="TextView"><Expressions/><location startLine="26" startOffset="8" endLine="35" endOffset="38"/></Target><Target id="@+id/staminaTextView" view="TextView"><Expressions/><location startLine="38" startOffset="8" endLine="47" endOffset="38"/></Target><Target id="@+id/firewoodTextView" view="TextView"><Expressions/><location startLine="50" startOffset="8" endLine="59" endOffset="38"/></Target><Target id="@+id/foodTextView" view="TextView"><Expressions/><location startLine="62" startOffset="8" endLine="71" endOffset="38"/></Target><Target id="@+id/storyScrollView" view="ScrollView"><Expressions/><location startLine="76" startOffset="4" endLine="103" endOffset="16"/></Target><Target id="@+id/storyTextView" view="TextView"><Expressions/><location startLine="92" startOffset="8" endLine="101" endOffset="37"/></Target><Target id="@+id/choicesLayout" view="LinearLayout"><Expressions/><location startLine="106" startOffset="4" endLine="157" endOffset="18"/></Target><Target id="@+id/choice1Button" view="Button"><Expressions/><location startLine="119" startOffset="8" endLine="129" endOffset="38"/></Target><Target id="@+id/choice2Button" view="Button"><Expressions/><location startLine="132" startOffset="8" endLine="142" endOffset="38"/></Target><Target id="@+id/choice3Button" view="Button"><Expressions/><location startLine="145" startOffset="8" endLine="155" endOffset="39"/></Target></Targets></Layout>