<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.ainative.mountainsurvival" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="207" endOffset="51"/></Target><Target id="@+id/statusBarLayout" view="LinearLayout"><Expressions/><location startLine="10" startOffset="4" endLine="121" endOffset="18"/></Target><Target id="@+id/warmthTextView" view="TextView"><Expressions/><location startLine="39" startOffset="12" endLine="46" endOffset="42"/></Target><Target id="@+id/staminaTextView" view="TextView"><Expressions/><location startLine="63" startOffset="12" endLine="70" endOffset="42"/></Target><Target id="@+id/firewoodTextView" view="TextView"><Expressions/><location startLine="87" startOffset="12" endLine="94" endOffset="42"/></Target><Target id="@+id/foodTextView" view="TextView"><Expressions/><location startLine="111" startOffset="12" endLine="118" endOffset="42"/></Target><Target id="@+id/storyScrollView" view="ScrollView"><Expressions/><location startLine="124" startOffset="4" endLine="151" endOffset="16"/></Target><Target id="@+id/storyTextView" view="TextView"><Expressions/><location startLine="140" startOffset="8" endLine="149" endOffset="37"/></Target><Target id="@+id/choicesLayout" view="LinearLayout"><Expressions/><location startLine="154" startOffset="4" endLine="205" endOffset="18"/></Target><Target id="@+id/choice1Button" view="Button"><Expressions/><location startLine="167" startOffset="8" endLine="177" endOffset="38"/></Target><Target id="@+id/choice2Button" view="Button"><Expressions/><location startLine="180" startOffset="8" endLine="190" endOffset="38"/></Target><Target id="@+id/choice3Button" view="Button"><Expressions/><location startLine="193" startOffset="8" endLine="203" endOffset="39"/></Target></Targets></Layout>