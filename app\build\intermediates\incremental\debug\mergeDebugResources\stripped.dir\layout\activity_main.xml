<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#2C3E50"
    tools:context=".MainActivity">

    <!-- 顶部状态栏 -->
    <LinearLayout
        android:id="@+id/statusBarLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:orientation="horizontal"
        android:weightSum="4"
        android:background="#34495E"
        android:padding="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 体温 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_fire"
                android:layout_marginEnd="4dp" />

            <TextView
                android:id="@+id/warmthTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="80"
                android:textColor="#E67E22"
                android:textSize="16sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- 体力 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_muscle"
                android:layout_marginEnd="4dp" />

            <TextView
                android:id="@+id/staminaTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="100"
                android:textColor="#ECF0F1"
                android:textSize="16sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- 木柴 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_wood"
                android:layout_marginEnd="4dp" />

            <TextView
                android:id="@+id/firewoodTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5"
                android:textColor="#ECF0F1"
                android:textSize="16sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- 食物 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_food"
                android:layout_marginEnd="4dp" />

            <TextView
                android:id="@+id/foodTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="3"
                android:textColor="#ECF0F1"
                android:textSize="16sp"
                android:textStyle="bold" />
        </LinearLayout>

    </LinearLayout>

    <!-- 故事文本区 - 包装在ScrollView中支持滚动 -->
    <ScrollView
        android:id="@+id/storyScrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:background="#34495E"
        android:fillViewport="true"
        android:scrollbars="vertical"
        app:layout_constraintBottom_toTopOf="@+id/choicesLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statusBarLayout">

        <TextView
            android:id="@+id/storyTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="top|start"
            android:lineSpacingExtra="4dp"
            android:padding="16dp"
            android:text="刺骨的寒风把你从昏迷中唤醒。你挣扎着睁开眼，发现自己正躺在一间破旧的小木屋里。屋外，暴风雪的呼啸声如同怪物的嘶吼。你检查了一下自己的状况，必须立刻想办法生火！"
            android:textColor="#ECF0F1"
            android:textSize="18sp" />

    </ScrollView>

    <!-- 选项按钮区 -->
    <LinearLayout
        android:id="@+id/choicesLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 选项按钮1 -->
        <Button
            android:id="@+id/choice1Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:backgroundTint="#95A5A6"
            android:padding="16dp"
            android:text="劈开旧家具生火"
            android:textColor="#2C3E50"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 选项按钮2 -->
        <Button
            android:id="@+id/choice2Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:backgroundTint="#95A5A6"
            android:padding="16dp"
            android:text="冒着风雪出去找柴火"
            android:textColor="#2C3E50"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 选项按钮3 -->
        <Button
            android:id="@+id/choice3Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="#95A5A6"
            android:padding="16dp"
            android:text="休息以保存体力"
            android:textColor="#2C3E50"
            android:textSize="16sp"
            android:textStyle="bold"
            android:visibility="gone" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
