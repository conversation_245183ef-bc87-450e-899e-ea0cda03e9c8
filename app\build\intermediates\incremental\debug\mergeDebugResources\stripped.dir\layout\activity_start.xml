<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="#1a1a1a"
    android:padding="32dp">

    <!-- 游戏标题 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="16dp">

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_snowflake"
            android:layout_marginEnd="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="雪山求生"
            android:textSize="36sp"
            android:textColor="#ffffff"
            android:textStyle="bold"
            android:gravity="center" />

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_snowflake"
            android:layout_marginStart="8dp" />
    </LinearLayout>

    <!-- 副标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Mountain Survival"
        android:textSize="18sp"
        android:textColor="#cccccc"
        android:layout_marginBottom="48dp"
        android:gravity="center" />

    <!-- 游戏描述 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginBottom="64dp">

        <!-- 描述项目1 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_mountain"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="你被困在雪山中的一间小屋里"
                android:textSize="16sp"
                android:textColor="#e0e0e0" />
        </LinearLayout>

        <!-- 描述项目2 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_fire"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="管理你的体温、体力和资源"
                android:textSize="16sp"
                android:textColor="#e0e0e0" />
        </LinearLayout>

        <!-- 描述项目3 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_clock"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="坚持7天等待救援"
                android:textSize="16sp"
                android:textColor="#e0e0e0" />
        </LinearLayout>

        <!-- 描述项目4 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="16dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_skull"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="体温或体力归零即死亡"
                android:textSize="16sp"
                android:textColor="#e0e0e0" />
        </LinearLayout>

        <!-- 挑战文字 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="你能在严酷的雪山中生存下来吗？"
            android:textSize="18sp"
            android:textColor="#ffffff"
            android:textStyle="bold"
            android:gravity="center" />
    </LinearLayout>

    <!-- 开始游戏按钮 -->
    <LinearLayout
        android:id="@+id/startGameButton"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@drawable/button_background"
        android:layout_marginBottom="16dp"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_gamepad"
            android:layout_marginEnd="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="开始游戏"
            android:textSize="18sp"
            android:textColor="#ffffff"
            android:textStyle="bold" />
    </LinearLayout>

    <!-- 退出游戏按钮 -->
    <LinearLayout
        android:id="@+id/exitGameButton"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@drawable/button_background_secondary"
        android:layout_marginBottom="32dp"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_door"
            android:layout_marginEnd="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="退出游戏"
            android:textSize="18sp"
            android:textColor="#ffffff"
            android:textStyle="bold" />
    </LinearLayout>

    <!-- 版本信息 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="版本 1.0.0"
        android:textSize="12sp"
        android:textColor="#888888"
        android:layout_marginTop="32dp" />

</LinearLayout>
