1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ainative.mountainsurvival"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:5:5-34:19
18        android:allowBackup="true"
18-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.MountainSurvival" >
29-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:13:9-54
30
31        <!-- 开始界面 - 启动Activity -->
32        <activity
32-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:16:9-26:20
33            android:name="com.ainative.mountainsurvival.StartActivity"
33-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:17:13-42
34            android:exported="true"
34-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:18:13-36
35            android:label="@string/app_name"
35-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:19:13-45
36            android:theme="@style/Theme.MountainSurvival" >
36-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:20:13-58
37            <intent-filter>
37-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:21:13-25:29
38                <action android:name="android.intent.action.MAIN" />
38-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:22:17-69
38-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:22:25-66
39
40                <category android:name="android.intent.category.LAUNCHER" />
40-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:24:17-77
40-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:24:27-74
41            </intent-filter>
42        </activity>
43
44        <!-- 主游戏界面 -->
45        <activity
45-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:29:9-33:61
46            android:name="com.ainative.mountainsurvival.MainActivity"
46-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:30:13-41
47            android:exported="false"
47-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:31:13-37
48            android:label="@string/app_name"
48-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:32:13-45
49            android:theme="@style/Theme.MountainSurvival" />
49-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:33:13-58
50        <activity
50-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
51            android:name="androidx.activity.ComponentActivity"
51-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
52            android:exported="true" />
52-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
53        <activity
53-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
54            android:name="androidx.compose.ui.tooling.PreviewActivity"
54-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
55            android:exported="true" />
55-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
56
57        <provider
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
58            android:name="androidx.startup.InitializationProvider"
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
59            android:authorities="com.ainative.mountainsurvival.androidx-startup"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
60            android:exported="false" >
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
61            <meta-data
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.emoji2.text.EmojiCompatInitializer"
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
63                android:value="androidx.startup" />
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
65-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
66                android:value="androidx.startup" />
66-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
67            <meta-data
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
68                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
69                android:value="androidx.startup" />
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
70        </provider>
71
72        <receiver
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
73            android:name="androidx.profileinstaller.ProfileInstallReceiver"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
74            android:directBootAware="false"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
75            android:enabled="true"
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
76            android:exported="true"
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
77            android:permission="android.permission.DUMP" >
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
79                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
82                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
85                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
88                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
89            </intent-filter>
90        </receiver>
91    </application>
92
93</manifest>
