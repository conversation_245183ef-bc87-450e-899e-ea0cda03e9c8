1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ainative.mountainsurvival"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:5:5-34:19
18        android:allowBackup="true"
18-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee81e9003baef06be7850ff4f00325b8\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:7:9-65
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:fullBackupContent="@xml/backup_rules"
23-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:8:9-54
24        android:icon="@mipmap/ic_launcher"
24-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:10:9-41
26        android:roundIcon="@mipmap/ic_launcher_round"
26-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:11:9-54
27        android:supportsRtl="true"
27-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:12:9-35
28        android:theme="@style/Theme.MountainSurvival" >
28-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:13:9-54
29
30        <!-- 开始界面 - 启动Activity -->
31        <activity
31-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:16:9-26:20
32            android:name="com.ainative.mountainsurvival.StartActivity"
32-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:17:13-42
33            android:exported="true"
33-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:18:13-36
34            android:label="@string/app_name"
34-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:19:13-45
35            android:theme="@style/Theme.MountainSurvival" >
35-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:20:13-58
36            <intent-filter>
36-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:21:13-25:29
37                <action android:name="android.intent.action.MAIN" />
37-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:22:17-69
37-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:22:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:24:17-77
39-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:24:27-74
40            </intent-filter>
41        </activity>
42
43        <!-- 主游戏界面 -->
44        <activity
44-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:29:9-33:61
45            android:name="com.ainative.mountainsurvival.MainActivity"
45-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:30:13-41
46            android:exported="false"
46-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:31:13-37
47            android:label="@string/app_name"
47-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:32:13-45
48            android:theme="@style/Theme.MountainSurvival" />
48-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:33:13-58
49        <activity
49-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
50            android:name="androidx.activity.ComponentActivity"
50-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
51            android:exported="true" />
51-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf0789ae4c448878dc74378ebdfb6958\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
52        <activity
52-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
53            android:name="androidx.compose.ui.tooling.PreviewActivity"
53-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
54            android:exported="true" />
54-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f38b5c402d23b30c2e2875c7c76f10e0\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
55
56        <provider
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
57            android:name="androidx.startup.InitializationProvider"
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
58            android:authorities="com.ainative.mountainsurvival.androidx-startup"
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
59            android:exported="false" >
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
60            <meta-data
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.emoji2.text.EmojiCompatInitializer"
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
62                android:value="androidx.startup" />
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f27ceb4a61ff296c8d63ebd994f71d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
64-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
65                android:value="androidx.startup" />
65-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\371a901e1b62b689ce41179655425bd7\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
66            <meta-data
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
68                android:value="androidx.startup" />
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
69        </provider>
70
71        <receiver
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
72            android:name="androidx.profileinstaller.ProfileInstallReceiver"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
73            android:directBootAware="false"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
74            android:enabled="true"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
75            android:exported="true"
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
76            android:permission="android.permission.DUMP" >
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
78                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
81                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
84                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
87                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5489a04eb87bf8bba9568350a1d1b87e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
88            </intent-filter>
89        </receiver>
90    </application>
91
92</manifest>
