[{"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_door.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_door.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_mountain.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_mountain.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_gamepad.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_gamepad.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\layout_activity_start.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\layout\\activity_start.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\drawable_button_background_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\drawable\\button_background_secondary.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_wood.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_wood.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_gift.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_gift.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_house.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_house.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/layout_activity_main.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/layout/activity_main.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_refresh.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_refresh.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_thermometer.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_thermometer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_muscle.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_muscle.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_skull.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_skull.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_clock.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_clock.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_calendar.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_calendar.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/layout_activity_start.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/layout/activity_start.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_trophy.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_trophy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\drawable\\ic_launcher_background.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_helicopter.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_helicopter.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_heart.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_heart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_snowflake.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_snowflake.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_food.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_food.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_fire.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_fire.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-debug-53:\\drawable_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.ainative.mountainsurvival.app-main-55:\\drawable\\button_background.xml"}, {"merged": "com.ainative.mountainsurvival.app-debug-53:/drawable_ic_checkmark.xml.flat", "source": "com.ainative.mountainsurvival.app-main-55:/drawable/ic_checkmark.xml"}]