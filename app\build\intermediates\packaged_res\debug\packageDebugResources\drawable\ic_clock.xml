<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">

    <!-- 表盘外圈 -->
    <path
        android:fillColor="#34495E"
        android:pathData="M12,2C17.5,2 22,6.5 22,12C22,17.5 17.5,22 12,22C6.5,22 2,17.5 2,12C2,6.5 6.5,2 12,2Z" />

    <!-- 表盘内圈 -->
    <path
        android:fillColor="#ECF0F1"
        android:pathData="M12,4C16.4,4 20,7.6 20,12C20,16.4 16.4,20 12,20C7.6,20 4,16.4 4,12C4,7.6 7.6,4 12,4Z" />

    <!-- 时针 -->
    <path
        android:fillColor="#E74C3C"
        android:pathData="M11,8L13,8L13,12L11,12Z" />

    <!-- 分针 -->
    <path
        android:fillColor="#E74C3C"
        android:pathData="M12,11L16,11L16,13L12,13Z" />

    <!-- 中心点 -->
    <path
        android:fillColor="#E74C3C"
        android:pathData="M11,11L13,11L13,13L11,13Z" />
</vector>
