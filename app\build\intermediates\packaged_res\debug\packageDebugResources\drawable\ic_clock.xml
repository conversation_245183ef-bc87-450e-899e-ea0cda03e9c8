<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">

    <!-- 表盘外圈 -->
    <circle
        android:fillColor="#34495E"
        android:cx="12"
        android:cy="12"
        android:radius="10" />

    <!-- 表盘内圈 -->
    <circle
        android:fillColor="#ECF0F1"
        android:cx="12"
        android:cy="12"
        android:radius="8" />

    <!-- 时针 -->
    <rect
        android:fillColor="#E74C3C"
        android:x="11"
        android:y="8"
        android:width="2"
        android:height="4" />

    <!-- 分针 -->
    <rect
        android:fillColor="#E74C3C"
        android:x="12"
        android:y="11"
        android:width="4"
        android:height="2" />

    <!-- 中心点 -->
    <circle
        android:fillColor="#E74C3C"
        android:cx="12"
        android:cy="12"
        android:radius="1" />
</vector>
