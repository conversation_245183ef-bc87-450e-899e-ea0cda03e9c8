<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">

    <!-- 手柄主体 -->
    <path
        android:fillColor="#4A90E2"
        android:pathData="M8,8C6,8 4,10 4,12L4,14C4,16 6,18 8,18L10,18C10,16 10,14 12,14C14,14 14,16 14,18L16,18C18,18 20,16 20,14L20,12C20,10 18,8 16,8L8,8Z" />

    <!-- 左侧手柄 -->
    <path
        android:fillColor="#357ABD"
        android:pathData="M4,12C2,12 2,14 4,14L4,16C4,18 6,20 8,20C10,20 12,18 12,16L12,14C12,12 10,12 8,12L4,12Z" />

    <!-- 右侧手柄 -->
    <path
        android:fillColor="#357ABD"
        android:pathData="M20,12C22,12 22,14 20,14L20,16C20,18 18,20 16,20C14,20 12,18 12,16L12,14C12,12 14,12 16,12L20,12Z" />

    <!-- 按钮 -->
    <circle
        android:fillColor="#FFFFFF"
        android:cx="8"
        android:cy="10"
        android:radius="1" />

    <circle
        android:fillColor="#FFFFFF"
        android:cx="16"
        android:cy="10"
        android:radius="1" />

    <!-- 十字键 -->
    <rect
        android:fillColor="#FFFFFF"
        android:x="6"
        android:y="12"
        android:width="2"
        android:height="2" />

    <!-- 功能按钮 -->
    <circle
        android:fillColor="#E74C3C"
        android:cx="18"
        android:cy="12"
        android:radius="1" />

    <circle
        android:fillColor="#F39C12"
        android:cx="17"
        android:cy="14"
        android:radius="1" />
</vector>
