<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <!-- 礼品盒主体 -->
    <rect
        android:fillColor="#E74C3C"
        android:x="5"
        android:y="10"
        android:width="14"
        android:height="10" />
    
    <!-- 礼品盒盖子 -->
    <rect
        android:fillColor="#C0392B"
        android:x="4"
        android:y="8"
        android:width="16"
        android:height="3" />
    
    <!-- 丝带垂直 -->
    <rect
        android:fillColor="#F1C40F"
        android:x="11"
        android:y="8"
        android:width="2"
        android:height="12" />
    
    <!-- 丝带水平 -->
    <rect
        android:fillColor="#F1C40F"
        android:x="4"
        android:y="9"
        android:width="16"
        android:height="2" />
    
    <!-- 蝴蝶结左侧 -->
    <path
        android:fillColor="#F39C12"
        android:pathData="M8,4C6,4 6,6 8,6C8,8 10,8 10,6C12,6 12,4 10,4C10,2 8,2 8,4Z" />
    
    <!-- 蝴蝶结右侧 -->
    <path
        android:fillColor="#F39C12"
        android:pathData="M16,4C18,4 18,6 16,6C16,8 14,8 14,6C12,6 12,4 14,4C14,2 16,2 16,4Z" />
    
    <!-- 蝴蝶结中心 -->
    <ellipse
        android:fillColor="#E67E22"
        android:cx="12"
        android:cy="5"
        android:rx="1"
        android:ry="2" />
</vector>
