com.ainative.mountainsurvival
anim abc_fade_in
anim abc_fade_out
anim abc_grow_fade_in_from_bottom
anim abc_popup_enter
anim abc_popup_exit
anim abc_shrink_fade_out_from_bottom
anim abc_slide_in_bottom
anim abc_slide_in_top
anim abc_slide_out_bottom
anim abc_slide_out_top
anim abc_tooltip_enter
anim abc_tooltip_exit
anim btn_checkbox_to_checked_box_inner_merged_animation
anim btn_checkbox_to_checked_box_outer_merged_animation
anim btn_checkbox_to_checked_icon_null_animation
anim btn_checkbox_to_unchecked_box_inner_merged_animation
anim btn_checkbox_to_unchecked_check_path_merged_animation
anim btn_checkbox_to_unchecked_icon_null_animation
anim btn_radio_to_off_mtrl_dot_group_animation
anim btn_radio_to_off_mtrl_ring_outer_animation
anim btn_radio_to_off_mtrl_ring_outer_path_animation
anim btn_radio_to_on_mtrl_dot_group_animation
anim btn_radio_to_on_mtrl_ring_outer_animation
anim btn_radio_to_on_mtrl_ring_outer_path_animation
anim fragment_fast_out_extra_slow_in
animator fragment_close_enter
animator fragment_close_exit
animator fragment_fade_enter
animator fragment_fade_exit
animator fragment_open_enter
animator fragment_open_exit
attr SharedValue
attr SharedValueId
attr actionBarDivider
attr actionBarItemBackground
attr actionBarPopupTheme
attr actionBarSize
attr actionBarSplitStyle
attr actionBarStyle
attr actionBarTabBarStyle
attr actionBarTabStyle
attr actionBarTabTextStyle
attr actionBarTheme
attr actionBarWidgetTheme
attr actionButtonStyle
attr actionDropDownStyle
attr actionLayout
attr actionMenuTextAppearance
attr actionMenuTextColor
attr actionModeBackground
attr actionModeCloseButtonStyle
attr actionModeCloseContentDescription
attr actionModeCloseDrawable
attr actionModeCopyDrawable
attr actionModeCutDrawable
attr actionModeFindDrawable
attr actionModePasteDrawable
attr actionModePopupWindowStyle
attr actionModeSelectAllDrawable
attr actionModeShareDrawable
attr actionModeSplitBackground
attr actionModeStyle
attr actionModeTheme
attr actionModeWebSearchDrawable
attr actionOverflowButtonStyle
attr actionOverflowMenuStyle
attr actionProviderClass
attr actionViewClass
attr activityChooserViewStyle
attr alertDialogButtonGroupStyle
attr alertDialogCenterButtons
attr alertDialogStyle
attr alertDialogTheme
attr allowStacking
attr alpha
attr alphabeticModifiers
attr altSrc
attr animateCircleAngleTo
attr animateRelativeTo
attr applyMotionScene
attr arcMode
attr arrowHeadLength
attr arrowShaftLength
attr attributeName
attr autoCompleteMode
attr autoCompleteTextViewStyle
attr autoSizeMaxTextSize
attr autoSizeMinTextSize
attr autoSizePresetSizes
attr autoSizeStepGranularity
attr autoSizeTextType
attr autoTransition
attr background
attr backgroundSplit
attr backgroundStacked
attr backgroundTint
attr backgroundTintMode
attr barLength
attr barrierAllowsGoneWidgets
attr barrierDirection
attr barrierMargin
attr blendSrc
attr borderRound
attr borderRoundPercent
attr borderlessButtonStyle
attr brightness
attr buttonBarButtonStyle
attr buttonBarNegativeButtonStyle
attr buttonBarNeutralButtonStyle
attr buttonBarPositiveButtonStyle
attr buttonBarStyle
attr buttonCompat
attr buttonGravity
attr buttonIconDimen
attr buttonPanelSideLayout
attr buttonStyle
attr buttonStyleSmall
attr buttonTint
attr buttonTintMode
attr carousel_backwardTransition
attr carousel_emptyViewsBehavior
attr carousel_firstView
attr carousel_forwardTransition
attr carousel_infinite
attr carousel_nextState
attr carousel_previousState
attr carousel_touchUpMode
attr carousel_touchUp_dampeningFactor
attr carousel_touchUp_velocityThreshold
attr chainUseRtl
attr checkMarkCompat
attr checkMarkTint
attr checkMarkTintMode
attr checkboxStyle
attr checkedTextViewStyle
attr circleRadius
attr circularflow_angles
attr circularflow_defaultAngle
attr circularflow_defaultRadius
attr circularflow_radiusInDP
attr circularflow_viewCenter
attr clearsTag
attr clickAction
attr closeIcon
attr closeItemLayout
attr collapseContentDescription
attr collapseIcon
attr color
attr colorAccent
attr colorBackgroundFloating
attr colorButtonNormal
attr colorControlActivated
attr colorControlHighlight
attr colorControlNormal
attr colorError
attr colorPrimary
attr colorPrimaryDark
attr colorSwitchThumbNormal
attr commitIcon
attr constraintRotate
attr constraintSet
attr constraintSetEnd
attr constraintSetStart
attr constraint_referenced_ids
attr constraint_referenced_tags
attr constraints
attr content
attr contentDescription
attr contentInsetEnd
attr contentInsetEndWithActions
attr contentInsetLeft
attr contentInsetRight
attr contentInsetStart
attr contentInsetStartWithNavigation
attr contrast
attr controlBackground
attr crossfade
attr currentState
attr curveFit
attr customBoolean
attr customColorDrawableValue
attr customColorValue
attr customDimension
attr customFloatValue
attr customIntegerValue
attr customNavigationLayout
attr customPixelDimension
attr customReference
attr customStringValue
attr defaultDuration
attr defaultQueryHint
attr defaultState
attr deltaPolarAngle
attr deltaPolarRadius
attr deriveConstraintsFrom
attr dialogCornerRadius
attr dialogPreferredPadding
attr dialogTheme
attr displayOptions
attr divider
attr dividerHorizontal
attr dividerPadding
attr dividerVertical
attr dragDirection
attr dragScale
attr dragThreshold
attr drawPath
attr drawableBottomCompat
attr drawableEndCompat
attr drawableLeftCompat
attr drawableRightCompat
attr drawableSize
attr drawableStartCompat
attr drawableTint
attr drawableTintMode
attr drawableTopCompat
attr drawerArrowStyle
attr dropDownListViewStyle
attr dropdownListPreferredItemHeight
attr duration
attr editTextBackground
attr editTextColor
attr editTextStyle
attr elevation
attr emojiCompatEnabled
attr expandActivityOverflowButtonDrawable
attr firstBaselineToTopHeight
attr flow_firstHorizontalBias
attr flow_firstHorizontalStyle
attr flow_firstVerticalBias
attr flow_firstVerticalStyle
attr flow_horizontalAlign
attr flow_horizontalBias
attr flow_horizontalGap
attr flow_horizontalStyle
attr flow_lastHorizontalBias
attr flow_lastHorizontalStyle
attr flow_lastVerticalBias
attr flow_lastVerticalStyle
attr flow_maxElementsWrap
attr flow_padding
attr flow_verticalAlign
attr flow_verticalBias
attr flow_verticalGap
attr flow_verticalStyle
attr flow_wrapMode
attr font
attr fontFamily
attr fontProviderAuthority
attr fontProviderCerts
attr fontProviderFetchStrategy
attr fontProviderFetchTimeout
attr fontProviderPackage
attr fontProviderQuery
attr fontProviderSystemFontFamily
attr fontStyle
attr fontVariationSettings
attr fontWeight
attr framePosition
attr gapBetweenBars
attr goIcon
attr guidelineUseRtl
attr height
attr hideOnContentScroll
attr homeAsUpIndicator
attr homeLayout
attr icon
attr iconTint
attr iconTintMode
attr iconifiedByDefault
attr ifTagNotSet
attr ifTagSet
attr imageButtonStyle
attr imagePanX
attr imagePanY
attr imageRotate
attr imageZoom
attr indeterminateProgressStyle
attr initialActivityCount
attr isLightTheme
attr itemPadding
attr keyPositionType
attr lStar
attr lastBaselineToBottomHeight
attr layout
attr layoutDescription
attr layoutDuringTransition
attr layout_constrainedHeight
attr layout_constrainedWidth
attr layout_constraintBaseline_creator
attr layout_constraintBaseline_toBaselineOf
attr layout_constraintBaseline_toBottomOf
attr layout_constraintBaseline_toTopOf
attr layout_constraintBottom_creator
attr layout_constraintBottom_toBottomOf
attr layout_constraintBottom_toTopOf
attr layout_constraintCircle
attr layout_constraintCircleAngle
attr layout_constraintCircleRadius
attr layout_constraintDimensionRatio
attr layout_constraintEnd_toEndOf
attr layout_constraintEnd_toStartOf
attr layout_constraintGuide_begin
attr layout_constraintGuide_end
attr layout_constraintGuide_percent
attr layout_constraintHeight
attr layout_constraintHeight_default
attr layout_constraintHeight_max
attr layout_constraintHeight_min
attr layout_constraintHeight_percent
attr layout_constraintHorizontal_bias
attr layout_constraintHorizontal_chainStyle
attr layout_constraintHorizontal_weight
attr layout_constraintLeft_creator
attr layout_constraintLeft_toLeftOf
attr layout_constraintLeft_toRightOf
attr layout_constraintRight_creator
attr layout_constraintRight_toLeftOf
attr layout_constraintRight_toRightOf
attr layout_constraintStart_toEndOf
attr layout_constraintStart_toStartOf
attr layout_constraintTag
attr layout_constraintTop_creator
attr layout_constraintTop_toBottomOf
attr layout_constraintTop_toTopOf
attr layout_constraintVertical_bias
attr layout_constraintVertical_chainStyle
attr layout_constraintVertical_weight
attr layout_constraintWidth
attr layout_constraintWidth_default
attr layout_constraintWidth_max
attr layout_constraintWidth_min
attr layout_constraintWidth_percent
attr layout_editor_absoluteX
attr layout_editor_absoluteY
attr layout_goneMarginBaseline
attr layout_goneMarginBottom
attr layout_goneMarginEnd
attr layout_goneMarginLeft
attr layout_goneMarginRight
attr layout_goneMarginStart
attr layout_goneMarginTop
attr layout_marginBaseline
attr layout_optimizationLevel
attr layout_wrapBehaviorInParent
attr limitBoundsTo
attr lineHeight
attr listChoiceBackgroundIndicator
attr listChoiceIndicatorMultipleAnimated
attr listChoiceIndicatorSingleAnimated
attr listDividerAlertDialog
attr listItemLayout
attr listLayout
attr listMenuViewStyle
attr listPopupWindowStyle
attr listPreferredItemHeight
attr listPreferredItemHeightLarge
attr listPreferredItemHeightSmall
attr listPreferredItemPaddingEnd
attr listPreferredItemPaddingLeft
attr listPreferredItemPaddingRight
attr listPreferredItemPaddingStart
attr logo
attr logoDescription
attr maxAcceleration
attr maxButtonHeight
attr maxHeight
attr maxVelocity
attr maxWidth
attr measureWithLargestChild
attr menu
attr methodName
attr minHeight
attr minWidth
attr mock_diagonalsColor
attr mock_label
attr mock_labelBackgroundColor
attr mock_labelColor
attr mock_showDiagonals
attr mock_showLabel
attr motionDebug
attr motionEffect_alpha
attr motionEffect_end
attr motionEffect_move
attr motionEffect_start
attr motionEffect_strict
attr motionEffect_translationX
attr motionEffect_translationY
attr motionEffect_viewTransition
attr motionInterpolator
attr motionPathRotate
attr motionProgress
attr motionStagger
attr motionTarget
attr motion_postLayoutCollision
attr motion_triggerOnCollision
attr moveWhenScrollAtTop
attr multiChoiceItemLayout
attr navigationContentDescription
attr navigationIcon
attr navigationMode
attr nestedScrollFlags
attr nestedScrollViewStyle
attr numericModifiers
attr onCross
attr onHide
attr onNegativeCross
attr onPositiveCross
attr onShow
attr onStateTransition
attr onTouchUp
attr overlapAnchor
attr overlay
attr paddingBottomNoButtons
attr paddingEnd
attr paddingStart
attr paddingTopNoTitle
attr panelBackground
attr panelMenuListTheme
attr panelMenuListWidth
attr pathMotionArc
attr path_percent
attr percentHeight
attr percentWidth
attr percentX
attr percentY
attr perpendicularPath_percent
attr pivotAnchor
attr placeholder_emptyVisibility
attr polarRelativeTo
attr popupMenuStyle
attr popupTheme
attr popupWindowStyle
attr preserveIconSpacing
attr progressBarPadding
attr progressBarStyle
attr quantizeMotionInterpolator
attr quantizeMotionPhase
attr quantizeMotionSteps
attr queryBackground
attr queryHint
attr queryPatterns
attr radioButtonStyle
attr ratingBarStyle
attr ratingBarStyleIndicator
attr ratingBarStyleSmall
attr reactiveGuide_animateChange
attr reactiveGuide_applyToAllConstraintSets
attr reactiveGuide_applyToConstraintSet
attr reactiveGuide_valueId
attr region_heightLessThan
attr region_heightMoreThan
attr region_widthLessThan
attr region_widthMoreThan
attr rotationCenterId
attr round
attr roundPercent
attr saturation
attr scaleFromTextSize
attr searchHintIcon
attr searchIcon
attr searchViewStyle
attr seekBarStyle
attr selectableItemBackground
attr selectableItemBackgroundBorderless
attr setsTag
attr shortcutMatchRequired
attr showAsAction
attr showDividers
attr showPaths
attr showText
attr showTitle
attr singleChoiceItemLayout
attr sizePercent
attr spinBars
attr spinnerDropDownItemStyle
attr spinnerStyle
attr splitTrack
attr springBoundary
attr springDamping
attr springMass
attr springStiffness
attr springStopThreshold
attr srcCompat
attr staggered
attr state_above_anchor
attr subMenuArrow
attr submitBackground
attr subtitle
attr subtitleTextAppearance
attr subtitleTextColor
attr subtitleTextStyle
attr suggestionRowLayout
attr switchMinWidth
attr switchPadding
attr switchStyle
attr switchTextAppearance
attr targetId
attr telltales_tailColor
attr telltales_tailScale
attr telltales_velocityMode
attr textAllCaps
attr textAppearanceLargePopupMenu
attr textAppearanceListItem
attr textAppearanceListItemSecondary
attr textAppearanceListItemSmall
attr textAppearancePopupMenuHeader
attr textAppearanceSearchResultSubtitle
attr textAppearanceSearchResultTitle
attr textAppearanceSmallPopupMenu
attr textBackground
attr textBackgroundPanX
attr textBackgroundPanY
attr textBackgroundRotate
attr textBackgroundZoom
attr textColorAlertDialogListItem
attr textColorSearchUrl
attr textFillColor
attr textLocale
attr textOutlineColor
attr textOutlineThickness
attr textPanX
attr textPanY
attr textureBlurFactor
attr textureEffect
attr textureHeight
attr textureWidth
attr theme
attr thickness
attr thumbTextPadding
attr thumbTint
attr thumbTintMode
attr tickMark
attr tickMarkTint
attr tickMarkTintMode
attr tint
attr tintMode
attr title
attr titleMargin
attr titleMarginBottom
attr titleMarginEnd
attr titleMarginStart
attr titleMarginTop
attr titleMargins
attr titleTextAppearance
attr titleTextColor
attr titleTextStyle
attr toolbarNavigationButtonStyle
attr toolbarStyle
attr tooltipForegroundColor
attr tooltipFrameBackground
attr tooltipText
attr touchAnchorId
attr touchAnchorSide
attr touchRegionId
attr track
attr trackTint
attr trackTintMode
attr transformPivotTarget
attr transitionDisable
attr transitionEasing
attr transitionFlags
attr transitionPathRotate
attr triggerId
attr triggerReceiver
attr triggerSlack
attr ttcIndex
attr upDuration
attr viewInflaterClass
attr viewTransitionMode
attr viewTransitionOnCross
attr viewTransitionOnNegativeCross
attr viewTransitionOnPositiveCross
attr visibilityMode
attr voiceIcon
attr warmth
attr waveDecay
attr waveOffset
attr wavePeriod
attr wavePhase
attr waveShape
attr waveVariesBy
attr windowActionBar
attr windowActionBarOverlay
attr windowActionModeOverlay
attr windowFixedHeightMajor
attr windowFixedHeightMinor
attr windowFixedWidthMajor
attr windowFixedWidthMinor
attr windowMinWidthMajor
attr windowMinWidthMinor
attr windowNoTitle
bool abc_action_bar_embed_tabs
bool abc_config_actionMenuItemAllCaps
color abc_background_cache_hint_selector_material_dark
color abc_background_cache_hint_selector_material_light
color abc_btn_colored_borderless_text_material
color abc_btn_colored_text_material
color abc_color_highlight_material
color abc_decor_view_status_guard
color abc_decor_view_status_guard_light
color abc_hint_foreground_material_dark
color abc_hint_foreground_material_light
color abc_primary_text_disable_only_material_dark
color abc_primary_text_disable_only_material_light
color abc_primary_text_material_dark
color abc_primary_text_material_light
color abc_search_url_text
color abc_search_url_text_normal
color abc_search_url_text_pressed
color abc_search_url_text_selected
color abc_secondary_text_material_dark
color abc_secondary_text_material_light
color abc_tint_btn_checkable
color abc_tint_default
color abc_tint_edittext
color abc_tint_seek_thumb
color abc_tint_spinner
color abc_tint_switch_track
color accent_material_dark
color accent_material_light
color accent_orange
color androidx_core_ripple_material_light
color androidx_core_secondary_text_default_material_light
color background_floating_material_dark
color background_floating_material_light
color background_light
color background_material_dark
color background_material_light
color black
color bright_foreground_disabled_material_dark
color bright_foreground_disabled_material_light
color bright_foreground_inverse_material_dark
color bright_foreground_inverse_material_light
color bright_foreground_material_dark
color bright_foreground_material_light
color button_material_dark
color button_material_light
color call_notification_answer_color
color call_notification_decline_color
color dim_foreground_disabled_material_dark
color dim_foreground_disabled_material_light
color dim_foreground_material_dark
color dim_foreground_material_light
color error_color_material_dark
color error_color_material_light
color firewood_color
color food_color
color foreground_material_dark
color foreground_material_light
color highlighted_text_material_dark
color highlighted_text_material_light
color material_blue_grey_800
color material_blue_grey_900
color material_blue_grey_950
color material_deep_teal_200
color material_deep_teal_500
color material_grey_100
color material_grey_300
color material_grey_50
color material_grey_600
color material_grey_800
color material_grey_850
color material_grey_900
color notification_action_color_filter
color notification_icon_bg_color
color primary_blue
color primary_blue_dark
color primary_dark_material_dark
color primary_dark_material_light
color primary_material_dark
color primary_material_light
color primary_text_default_material_dark
color primary_text_default_material_light
color primary_text_disabled_material_dark
color primary_text_disabled_material_light
color purple_200
color purple_500
color purple_700
color ripple_material_dark
color ripple_material_light
color secondary_text_default_material_dark
color secondary_text_default_material_light
color secondary_text_disabled_material_dark
color secondary_text_disabled_material_light
color stamina_color
color switch_thumb_disabled_material_dark
color switch_thumb_disabled_material_light
color switch_thumb_material_dark
color switch_thumb_material_light
color switch_thumb_normal_material_dark
color switch_thumb_normal_material_light
color teal_200
color teal_700
color text_primary
color text_secondary
color tooltip_background_dark
color tooltip_background_light
color vector_tint_color
color vector_tint_theme_color
color warmth_color
color white
dimen abc_action_bar_content_inset_material
dimen abc_action_bar_content_inset_with_nav
dimen abc_action_bar_default_height_material
dimen abc_action_bar_default_padding_end_material
dimen abc_action_bar_default_padding_start_material
dimen abc_action_bar_elevation_material
dimen abc_action_bar_icon_vertical_padding_material
dimen abc_action_bar_overflow_padding_end_material
dimen abc_action_bar_overflow_padding_start_material
dimen abc_action_bar_stacked_max_height
dimen abc_action_bar_stacked_tab_max_width
dimen abc_action_bar_subtitle_bottom_margin_material
dimen abc_action_bar_subtitle_top_margin_material
dimen abc_action_button_min_height_material
dimen abc_action_button_min_width_material
dimen abc_action_button_min_width_overflow_material
dimen abc_alert_dialog_button_bar_height
dimen abc_alert_dialog_button_dimen
dimen abc_button_inset_horizontal_material
dimen abc_button_inset_vertical_material
dimen abc_button_padding_horizontal_material
dimen abc_button_padding_vertical_material
dimen abc_cascading_menus_min_smallest_width
dimen abc_config_prefDialogWidth
dimen abc_control_corner_material
dimen abc_control_inset_material
dimen abc_control_padding_material
dimen abc_dialog_corner_radius_material
dimen abc_dialog_fixed_height_major
dimen abc_dialog_fixed_height_minor
dimen abc_dialog_fixed_width_major
dimen abc_dialog_fixed_width_minor
dimen abc_dialog_list_padding_bottom_no_buttons
dimen abc_dialog_list_padding_top_no_title
dimen abc_dialog_min_width_major
dimen abc_dialog_min_width_minor
dimen abc_dialog_padding_material
dimen abc_dialog_padding_top_material
dimen abc_dialog_title_divider_material
dimen abc_disabled_alpha_material_dark
dimen abc_disabled_alpha_material_light
dimen abc_dropdownitem_icon_width
dimen abc_dropdownitem_text_padding_left
dimen abc_dropdownitem_text_padding_right
dimen abc_edit_text_inset_bottom_material
dimen abc_edit_text_inset_horizontal_material
dimen abc_edit_text_inset_top_material
dimen abc_floating_window_z
dimen abc_list_item_height_large_material
dimen abc_list_item_height_material
dimen abc_list_item_height_small_material
dimen abc_list_item_padding_horizontal_material
dimen abc_panel_menu_list_width
dimen abc_progress_bar_height_material
dimen abc_search_view_preferred_height
dimen abc_search_view_preferred_width
dimen abc_seekbar_track_background_height_material
dimen abc_seekbar_track_progress_height_material
dimen abc_select_dialog_padding_start_material
dimen abc_star_big
dimen abc_star_medium
dimen abc_star_small
dimen abc_switch_padding
dimen abc_text_size_body_1_material
dimen abc_text_size_body_2_material
dimen abc_text_size_button_material
dimen abc_text_size_caption_material
dimen abc_text_size_display_1_material
dimen abc_text_size_display_2_material
dimen abc_text_size_display_3_material
dimen abc_text_size_display_4_material
dimen abc_text_size_headline_material
dimen abc_text_size_large_material
dimen abc_text_size_medium_material
dimen abc_text_size_menu_header_material
dimen abc_text_size_menu_material
dimen abc_text_size_small_material
dimen abc_text_size_subhead_material
dimen abc_text_size_subtitle_material_toolbar
dimen abc_text_size_title_material
dimen abc_text_size_title_material_toolbar
dimen compat_button_inset_horizontal_material
dimen compat_button_inset_vertical_material
dimen compat_button_padding_horizontal_material
dimen compat_button_padding_vertical_material
dimen compat_control_corner_material
dimen compat_notification_large_icon_max_height
dimen compat_notification_large_icon_max_width
dimen disabled_alpha_material_dark
dimen disabled_alpha_material_light
dimen highlight_alpha_material_colored
dimen highlight_alpha_material_dark
dimen highlight_alpha_material_light
dimen hint_alpha_material_dark
dimen hint_alpha_material_light
dimen hint_pressed_alpha_material_dark
dimen hint_pressed_alpha_material_light
dimen notification_action_icon_size
dimen notification_action_text_size
dimen notification_big_circle_margin
dimen notification_content_margin_start
dimen notification_large_icon_height
dimen notification_large_icon_width
dimen notification_main_column_padding_top
dimen notification_media_narrow_margin
dimen notification_right_icon_size
dimen notification_right_side_padding_top
dimen notification_small_icon_background_padding
dimen notification_small_icon_size_as_large
dimen notification_subtext_size
dimen notification_top_pad
dimen notification_top_pad_large_text
dimen tooltip_corner_radius
dimen tooltip_horizontal_padding
dimen tooltip_margin
dimen tooltip_precise_anchor_extra_offset
dimen tooltip_precise_anchor_threshold
dimen tooltip_vertical_padding
dimen tooltip_y_offset_non_touch
dimen tooltip_y_offset_touch
drawable abc_ab_share_pack_mtrl_alpha
drawable abc_action_bar_item_background_material
drawable abc_btn_borderless_material
drawable abc_btn_check_material
drawable abc_btn_check_material_anim
drawable abc_btn_check_to_on_mtrl_000
drawable abc_btn_check_to_on_mtrl_015
drawable abc_btn_colored_material
drawable abc_btn_default_mtrl_shape
drawable abc_btn_radio_material
drawable abc_btn_radio_material_anim
drawable abc_btn_radio_to_on_mtrl_000
drawable abc_btn_radio_to_on_mtrl_015
drawable abc_btn_switch_to_on_mtrl_00001
drawable abc_btn_switch_to_on_mtrl_00012
drawable abc_cab_background_internal_bg
drawable abc_cab_background_top_material
drawable abc_cab_background_top_mtrl_alpha
drawable abc_control_background_material
drawable abc_dialog_material_background
drawable abc_edit_text_material
drawable abc_ic_ab_back_material
drawable abc_ic_arrow_drop_right_black_24dp
drawable abc_ic_clear_material
drawable abc_ic_commit_search_api_mtrl_alpha
drawable abc_ic_go_search_api_material
drawable abc_ic_menu_copy_mtrl_am_alpha
drawable abc_ic_menu_cut_mtrl_alpha
drawable abc_ic_menu_overflow_material
drawable abc_ic_menu_paste_mtrl_am_alpha
drawable abc_ic_menu_selectall_mtrl_alpha
drawable abc_ic_menu_share_mtrl_alpha
drawable abc_ic_search_api_material
drawable abc_ic_voice_search_api_material
drawable abc_item_background_holo_dark
drawable abc_item_background_holo_light
drawable abc_list_divider_material
drawable abc_list_divider_mtrl_alpha
drawable abc_list_focused_holo
drawable abc_list_longpressed_holo
drawable abc_list_pressed_holo_dark
drawable abc_list_pressed_holo_light
drawable abc_list_selector_background_transition_holo_dark
drawable abc_list_selector_background_transition_holo_light
drawable abc_list_selector_disabled_holo_dark
drawable abc_list_selector_disabled_holo_light
drawable abc_list_selector_holo_dark
drawable abc_list_selector_holo_light
drawable abc_menu_hardkey_panel_mtrl_mult
drawable abc_popup_background_mtrl_mult
drawable abc_ratingbar_indicator_material
drawable abc_ratingbar_material
drawable abc_ratingbar_small_material
drawable abc_scrubber_control_off_mtrl_alpha
drawable abc_scrubber_control_to_pressed_mtrl_000
drawable abc_scrubber_control_to_pressed_mtrl_005
drawable abc_scrubber_primary_mtrl_alpha
drawable abc_scrubber_track_mtrl_alpha
drawable abc_seekbar_thumb_material
drawable abc_seekbar_tick_mark_material
drawable abc_seekbar_track_material
drawable abc_spinner_mtrl_am_alpha
drawable abc_spinner_textfield_background_material
drawable abc_star_black_48dp
drawable abc_star_half_black_48dp
drawable abc_switch_thumb_material
drawable abc_switch_track_mtrl_alpha
drawable abc_tab_indicator_material
drawable abc_tab_indicator_mtrl_alpha
drawable abc_text_cursor_material
drawable abc_text_select_handle_left_mtrl
drawable abc_text_select_handle_middle_mtrl
drawable abc_text_select_handle_right_mtrl
drawable abc_textfield_activated_mtrl_alpha
drawable abc_textfield_default_mtrl_alpha
drawable abc_textfield_search_activated_mtrl_alpha
drawable abc_textfield_search_default_mtrl_alpha
drawable abc_textfield_search_material
drawable abc_vector_test
drawable btn_checkbox_checked_mtrl
drawable btn_checkbox_checked_to_unchecked_mtrl_animation
drawable btn_checkbox_unchecked_mtrl
drawable btn_checkbox_unchecked_to_checked_mtrl_animation
drawable btn_radio_off_mtrl
drawable btn_radio_off_to_on_mtrl_animation
drawable btn_radio_on_mtrl
drawable btn_radio_on_to_off_mtrl_animation
drawable button_background
drawable button_background_secondary
drawable ic_calendar
drawable ic_call_answer
drawable ic_call_answer_low
drawable ic_call_answer_video
drawable ic_call_answer_video_low
drawable ic_call_decline
drawable ic_call_decline_low
drawable ic_checkmark
drawable ic_clock
drawable ic_door
drawable ic_fire
drawable ic_food
drawable ic_gamepad
drawable ic_gift
drawable ic_heart
drawable ic_helicopter
drawable ic_house
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_mountain
drawable ic_muscle
drawable ic_refresh
drawable ic_skull
drawable ic_snowflake
drawable ic_thermometer
drawable ic_trophy
drawable ic_wood
drawable notification_action_background
drawable notification_bg
drawable notification_bg_low
drawable notification_bg_low_normal
drawable notification_bg_low_pressed
drawable notification_bg_normal
drawable notification_bg_normal_pressed
drawable notification_icon_background
drawable notification_oversize_large_icon_bg
drawable notification_template_icon_bg
drawable notification_template_icon_low_bg
drawable notification_tile_bg
drawable notify_panel_notification_icon_bg
drawable test_level_drawable
drawable tooltip_frame_dark
drawable tooltip_frame_light
id ALT
id CTRL
id FUNCTION
id META
id NO_DEBUG
id SHIFT
id SHOW_ALL
id SHOW_PATH
id SHOW_PROGRESS
id SYM
id accelerate
id accessibility_action_clickable_span
id accessibility_custom_action_0
id accessibility_custom_action_1
id accessibility_custom_action_10
id accessibility_custom_action_11
id accessibility_custom_action_12
id accessibility_custom_action_13
id accessibility_custom_action_14
id accessibility_custom_action_15
id accessibility_custom_action_16
id accessibility_custom_action_17
id accessibility_custom_action_18
id accessibility_custom_action_19
id accessibility_custom_action_2
id accessibility_custom_action_20
id accessibility_custom_action_21
id accessibility_custom_action_22
id accessibility_custom_action_23
id accessibility_custom_action_24
id accessibility_custom_action_25
id accessibility_custom_action_26
id accessibility_custom_action_27
id accessibility_custom_action_28
id accessibility_custom_action_29
id accessibility_custom_action_3
id accessibility_custom_action_30
id accessibility_custom_action_31
id accessibility_custom_action_4
id accessibility_custom_action_5
id accessibility_custom_action_6
id accessibility_custom_action_7
id accessibility_custom_action_8
id accessibility_custom_action_9
id actionDown
id actionDownUp
id actionUp
id action_bar
id action_bar_activity_content
id action_bar_container
id action_bar_root
id action_bar_spinner
id action_bar_subtitle
id action_bar_title
id action_container
id action_context_bar
id action_divider
id action_image
id action_menu_divider
id action_menu_presenter
id action_mode_bar
id action_mode_bar_stub
id action_mode_close_button
id action_text
id actions
id activity_chooser_view_content
id add
id alertTitle
id aligned
id allStates
id always
id androidx_compose_ui_view_composition_context
id animateToEnd
id animateToStart
id antiClockwise
id anticipate
id asConfigured
id async
id auto
id autoComplete
id autoCompleteToEnd
id autoCompleteToStart
id barrier
id baseline
id beginOnFirstDraw
id beginning
id bestChoice
id blocking
id bottom
id bounce
id bounceBoth
id bounceEnd
id bounceStart
id buttonPanel
id cache_measures
id callMeasure
id carryVelocity
id center
id center_vertical
id chain
id chain2
id chains
id checkbox
id checked
id choice1Button
id choice2Button
id choice3Button
id choicesLayout
id chronometer
id clockwise
id closest
id collapseActionView
id compose_view_saveable_id_tag
id constraint
id consume_window_insets_tag
id content
id contentPanel
id continuousVelocity
id cos
id currentState
id custom
id customPanel
id decelerate
id decelerateAndComplete
id decor_content_parent
id default_activity_button
id deltaRelative
id dependency_ordering
id dialog_button
id dimensions
id direct
id disableHome
id disableIntraAutoTransition
id disablePostScroll
id disableScroll
id dragAnticlockwise
id dragClockwise
id dragDown
id dragEnd
id dragLeft
id dragRight
id dragStart
id dragUp
id easeIn
id easeInOut
id easeOut
id east
id edit_query
id edit_text_id
id end
id exitGameButton
id expand_activities_button
id expanded_menu
id firewoodTextView
id flip
id foodTextView
id forever
id fragment_container_view_tag
id frost
id gone
id graph
id graph_wrap
id group_divider
id grouping
id groups
id hide_graphics_layer_in_inspector_tag
id hide_ime_id
id hide_in_inspector_tag
id home
id homeAsUp
id honorRequest
id horizontal_only
id icon
id icon_group
id ifRoom
id ignore
id ignoreRequest
id image
id immediateStop
id included
id info
id inspection_slot_table_set
id invisible
id is_pooling_container_tag
id italic
id jumpToEnd
id jumpToStart
id layout
id left
id legacy
id line1
id line3
id linear
id listMode
id list_item
id match_constraint
id match_parent
id message
id middle
id motion_base
id multiply
id never
id neverCompleteToEnd
id neverCompleteToStart
id noState
id none
id normal
id north
id notification_background
id notification_main_column
id notification_main_column_container
id off
id on
id onInterceptTouchReturnSwipe
id overshoot
id packed
id parent
id parentPanel
id parentRelative
id path
id pathRelative
id percent
id pooling_container_listener_holder_tag
id position
id postLayout
id progress_circular
id progress_horizontal
id radio
id ratio
id rectangles
id report_drawn
id reverseSawtooth
id right
id right_icon
id right_side
id sawtooth
id screen
id scrollIndicatorDown
id scrollIndicatorUp
id scrollView
id search_badge
id search_bar
id search_button
id search_close_btn
id search_edit_frame
id search_go_btn
id search_mag_icon
id search_plate
id search_src_text
id search_voice_btn
id select_dialog_listview
id sharedValueSet
id sharedValueUnset
id shortcut
id showCustom
id showHome
id showTitle
id sin
id skipped
id south
id spacer
id special_effects_controller_view_tag
id spline
id split_action_bar
id spread
id spread_inside
id spring
id square
id src_atop
id src_in
id src_over
id staminaTextView
id standard
id start
id startGameButton
id startHorizontal
id startVertical
id staticLayout
id staticPostLayout
id statusBarLayout
id stop
id storyScrollView
id storyTextView
id submenuarrow
id submit_area
id supportScrollUp
id tabMode
id tag_accessibility_actions
id tag_accessibility_clickable_spans
id tag_accessibility_heading
id tag_accessibility_pane_title
id tag_on_apply_window_listener
id tag_on_receive_content_listener
id tag_on_receive_content_mime_types
id tag_screen_reader_focusable
id tag_state_description
id tag_transition_group
id tag_unhandled_key_event_manager
id tag_unhandled_key_listeners
id tag_window_insets_animation_callback
id text
id text2
id textSpacerNoButtons
id textSpacerNoTitle
id time
id title
id titleDividerNoCustom
id title_template
id toggle
id top
id topPanel
id transitionToEnd
id transitionToStart
id triangle
id unchecked
id uniform
id up
id useLogo
id vertical_only
id view_transition
id view_tree_lifecycle_owner
id view_tree_on_back_pressed_dispatcher_owner
id view_tree_saved_state_registry_owner
id view_tree_view_model_store_owner
id visible
id visible_removing_fragment_view_tag
id warmthTextView
id west
id withText
id wrap
id wrap_content
id wrap_content_constrained
id wrapped_composition_tag
id x_left
id x_right
integer abc_config_activityDefaultDur
integer abc_config_activityShortDur
integer cancel_button_image_alpha
integer config_tooltipAnimTime
integer m3c_window_layout_in_display_cutout_mode
integer status_bar_notification_info_maxnum
interpolator btn_checkbox_checked_mtrl_animation_interpolator_0
interpolator btn_checkbox_checked_mtrl_animation_interpolator_1
interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0
interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1
interpolator btn_radio_to_off_mtrl_animation_interpolator_0
interpolator btn_radio_to_on_mtrl_animation_interpolator_0
interpolator fast_out_slow_in
layout abc_action_bar_title_item
layout abc_action_bar_up_container
layout abc_action_menu_item_layout
layout abc_action_menu_layout
layout abc_action_mode_bar
layout abc_action_mode_close_item_material
layout abc_activity_chooser_view
layout abc_activity_chooser_view_list_item
layout abc_alert_dialog_button_bar_material
layout abc_alert_dialog_material
layout abc_alert_dialog_title_material
layout abc_cascading_menu_item_layout
layout abc_dialog_title_material
layout abc_expanded_menu_layout
layout abc_list_menu_item_checkbox
layout abc_list_menu_item_icon
layout abc_list_menu_item_layout
layout abc_list_menu_item_radio
layout abc_popup_menu_header_item_layout
layout abc_popup_menu_item_layout
layout abc_screen_content_include
layout abc_screen_simple
layout abc_screen_simple_overlay_action_mode
layout abc_screen_toolbar
layout abc_search_dropdown_item_icons_2line
layout abc_search_view
layout abc_select_dialog_material
layout abc_tooltip
layout activity_main
layout activity_start
layout custom_dialog
layout ime_base_split_test_activity
layout ime_secondary_split_test_activity
layout notification_action
layout notification_action_tombstone
layout notification_template_custom_big
layout notification_template_icon_group
layout notification_template_part_chronometer
layout notification_template_part_time
layout select_dialog_item_material
layout select_dialog_multichoice_material
layout select_dialog_singlechoice_material
layout support_simple_spinner_dropdown_item
mipmap ic_launcher
mipmap ic_launcher_round
string abc_action_bar_home_description
string abc_action_bar_up_description
string abc_action_menu_overflow_description
string abc_action_mode_done
string abc_activity_chooser_view_see_all
string abc_activitychooserview_choose_application
string abc_capital_off
string abc_capital_on
string abc_menu_alt_shortcut_label
string abc_menu_ctrl_shortcut_label
string abc_menu_delete_shortcut_label
string abc_menu_enter_shortcut_label
string abc_menu_function_shortcut_label
string abc_menu_meta_shortcut_label
string abc_menu_shift_shortcut_label
string abc_menu_space_shortcut_label
string abc_menu_sym_shortcut_label
string abc_prepend_shortcut_label
string abc_search_hint
string abc_searchview_description_clear
string abc_searchview_description_query
string abc_searchview_description_search
string abc_searchview_description_submit
string abc_searchview_description_voice
string abc_shareactionprovider_share_with
string abc_shareactionprovider_share_with_application
string abc_toolbar_collapse_description
string androidx_startup
string app_name
string call_notification_answer_action
string call_notification_answer_video_action
string call_notification_decline_action
string call_notification_hang_up_action
string call_notification_incoming_text
string call_notification_ongoing_text
string call_notification_screening_text
string close_drawer
string close_sheet
string default_error_message
string default_popup_window_title
string dropdown_menu
string in_progress
string indeterminate
string m3c_bottom_sheet_collapse_description
string m3c_bottom_sheet_dismiss_description
string m3c_bottom_sheet_drag_handle_description
string m3c_bottom_sheet_expand_description
string m3c_bottom_sheet_pane_title
string m3c_date_input_headline
string m3c_date_input_headline_description
string m3c_date_input_invalid_for_pattern
string m3c_date_input_invalid_not_allowed
string m3c_date_input_invalid_year_range
string m3c_date_input_label
string m3c_date_input_no_input_description
string m3c_date_input_title
string m3c_date_picker_headline
string m3c_date_picker_headline_description
string m3c_date_picker_navigate_to_year_description
string m3c_date_picker_no_selection_description
string m3c_date_picker_scroll_to_earlier_years
string m3c_date_picker_scroll_to_later_years
string m3c_date_picker_switch_to_calendar_mode
string m3c_date_picker_switch_to_day_selection
string m3c_date_picker_switch_to_input_mode
string m3c_date_picker_switch_to_next_month
string m3c_date_picker_switch_to_previous_month
string m3c_date_picker_switch_to_year_selection
string m3c_date_picker_title
string m3c_date_picker_today_description
string m3c_date_picker_year_picker_pane_title
string m3c_date_range_input_invalid_range_input
string m3c_date_range_input_title
string m3c_date_range_picker_day_in_range
string m3c_date_range_picker_end_headline
string m3c_date_range_picker_scroll_to_next_month
string m3c_date_range_picker_scroll_to_previous_month
string m3c_date_range_picker_start_headline
string m3c_date_range_picker_title
string m3c_dialog
string m3c_dropdown_menu_collapsed
string m3c_dropdown_menu_expanded
string m3c_dropdown_menu_toggle
string m3c_search_bar_search
string m3c_snackbar_dismiss
string m3c_suggestions_available
string m3c_time_picker_am
string m3c_time_picker_hour
string m3c_time_picker_hour_24h_suffix
string m3c_time_picker_hour_selection
string m3c_time_picker_hour_suffix
string m3c_time_picker_hour_text_field
string m3c_time_picker_minute
string m3c_time_picker_minute_selection
string m3c_time_picker_minute_suffix
string m3c_time_picker_minute_text_field
string m3c_time_picker_period_toggle_description
string m3c_time_picker_pm
string m3c_tooltip_long_press_label
string m3c_tooltip_pane_description
string navigation_menu
string not_selected
string range_end
string range_start
string search_menu_title
string selected
string state_empty
string state_off
string state_on
string status_bar_notification_info_overflow
string switch_role
string tab
string template_percent
string tooltip_description
string tooltip_label
style AlertDialog_AppCompat
style AlertDialog_AppCompat_Light
style Animation_AppCompat_Dialog
style Animation_AppCompat_DropDownUp
style Animation_AppCompat_Tooltip
style Base_AlertDialog_AppCompat
style Base_AlertDialog_AppCompat_Light
style Base_Animation_AppCompat_Dialog
style Base_Animation_AppCompat_DropDownUp
style Base_Animation_AppCompat_Tooltip
style Base_DialogWindowTitle_AppCompat
style Base_DialogWindowTitleBackground_AppCompat
style Base_TextAppearance_AppCompat
style Base_TextAppearance_AppCompat_Body1
style Base_TextAppearance_AppCompat_Body2
style Base_TextAppearance_AppCompat_Button
style Base_TextAppearance_AppCompat_Caption
style Base_TextAppearance_AppCompat_Display1
style Base_TextAppearance_AppCompat_Display2
style Base_TextAppearance_AppCompat_Display3
style Base_TextAppearance_AppCompat_Display4
style Base_TextAppearance_AppCompat_Headline
style Base_TextAppearance_AppCompat_Inverse
style Base_TextAppearance_AppCompat_Large
style Base_TextAppearance_AppCompat_Large_Inverse
style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
style Base_TextAppearance_AppCompat_Medium
style Base_TextAppearance_AppCompat_Medium_Inverse
style Base_TextAppearance_AppCompat_Menu
style Base_TextAppearance_AppCompat_SearchResult
style Base_TextAppearance_AppCompat_SearchResult_Subtitle
style Base_TextAppearance_AppCompat_SearchResult_Title
style Base_TextAppearance_AppCompat_Small
style Base_TextAppearance_AppCompat_Small_Inverse
style Base_TextAppearance_AppCompat_Subhead
style Base_TextAppearance_AppCompat_Subhead_Inverse
style Base_TextAppearance_AppCompat_Title
style Base_TextAppearance_AppCompat_Title_Inverse
style Base_TextAppearance_AppCompat_Tooltip
style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu
style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle
style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
style Base_TextAppearance_AppCompat_Widget_ActionBar_Title
style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle
style Base_TextAppearance_AppCompat_Widget_ActionMode_Title
style Base_TextAppearance_AppCompat_Widget_Button
style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored
style Base_TextAppearance_AppCompat_Widget_Button_Colored
style Base_TextAppearance_AppCompat_Widget_Button_Inverse
style Base_TextAppearance_AppCompat_Widget_DropDownItem
style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header
style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large
style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small
style Base_TextAppearance_AppCompat_Widget_Switch
style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem
style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item
style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle
style Base_TextAppearance_Widget_AppCompat_Toolbar_Title
style Base_Theme_AppCompat
style Base_Theme_AppCompat_CompactMenu
style Base_Theme_AppCompat_Dialog
style Base_Theme_AppCompat_Dialog_Alert
style Base_Theme_AppCompat_Dialog_FixedSize
style Base_Theme_AppCompat_Dialog_MinWidth
style Base_Theme_AppCompat_DialogWhenLarge
style Base_Theme_AppCompat_Light
style Base_Theme_AppCompat_Light_DarkActionBar
style Base_Theme_AppCompat_Light_Dialog
style Base_Theme_AppCompat_Light_Dialog_Alert
style Base_Theme_AppCompat_Light_Dialog_FixedSize
style Base_Theme_AppCompat_Light_Dialog_MinWidth
style Base_Theme_AppCompat_Light_DialogWhenLarge
style Base_ThemeOverlay_AppCompat
style Base_ThemeOverlay_AppCompat_ActionBar
style Base_ThemeOverlay_AppCompat_Dark
style Base_ThemeOverlay_AppCompat_Dark_ActionBar
style Base_ThemeOverlay_AppCompat_Dialog
style Base_ThemeOverlay_AppCompat_Dialog_Alert
style Base_ThemeOverlay_AppCompat_Light
style Base_V21_Theme_AppCompat
style Base_V21_Theme_AppCompat_Dialog
style Base_V21_Theme_AppCompat_Light
style Base_V21_Theme_AppCompat_Light_Dialog
style Base_V21_ThemeOverlay_AppCompat_Dialog
style Base_V22_Theme_AppCompat
style Base_V22_Theme_AppCompat_Light
style Base_V23_Theme_AppCompat
style Base_V23_Theme_AppCompat_Light
style Base_V26_Theme_AppCompat
style Base_V26_Theme_AppCompat_Light
style Base_V26_Widget_AppCompat_Toolbar
style Base_V28_Theme_AppCompat
style Base_V28_Theme_AppCompat_Light
style Base_V7_Theme_AppCompat
style Base_V7_Theme_AppCompat_Dialog
style Base_V7_Theme_AppCompat_Light
style Base_V7_Theme_AppCompat_Light_Dialog
style Base_V7_ThemeOverlay_AppCompat_Dialog
style Base_V7_Widget_AppCompat_AutoCompleteTextView
style Base_V7_Widget_AppCompat_EditText
style Base_V7_Widget_AppCompat_Toolbar
style Base_Widget_AppCompat_ActionBar
style Base_Widget_AppCompat_ActionBar_Solid
style Base_Widget_AppCompat_ActionBar_TabBar
style Base_Widget_AppCompat_ActionBar_TabText
style Base_Widget_AppCompat_ActionBar_TabView
style Base_Widget_AppCompat_ActionButton
style Base_Widget_AppCompat_ActionButton_CloseMode
style Base_Widget_AppCompat_ActionButton_Overflow
style Base_Widget_AppCompat_ActionMode
style Base_Widget_AppCompat_ActivityChooserView
style Base_Widget_AppCompat_AutoCompleteTextView
style Base_Widget_AppCompat_Button
style Base_Widget_AppCompat_Button_Borderless
style Base_Widget_AppCompat_Button_Borderless_Colored
style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog
style Base_Widget_AppCompat_Button_Colored
style Base_Widget_AppCompat_Button_Small
style Base_Widget_AppCompat_ButtonBar
style Base_Widget_AppCompat_ButtonBar_AlertDialog
style Base_Widget_AppCompat_CompoundButton_CheckBox
style Base_Widget_AppCompat_CompoundButton_RadioButton
style Base_Widget_AppCompat_CompoundButton_Switch
style Base_Widget_AppCompat_DrawerArrowToggle
style Base_Widget_AppCompat_DrawerArrowToggle_Common
style Base_Widget_AppCompat_DropDownItem_Spinner
style Base_Widget_AppCompat_EditText
style Base_Widget_AppCompat_ImageButton
style Base_Widget_AppCompat_Light_ActionBar
style Base_Widget_AppCompat_Light_ActionBar_Solid
style Base_Widget_AppCompat_Light_ActionBar_TabBar
style Base_Widget_AppCompat_Light_ActionBar_TabText
style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse
style Base_Widget_AppCompat_Light_ActionBar_TabView
style Base_Widget_AppCompat_Light_PopupMenu
style Base_Widget_AppCompat_Light_PopupMenu_Overflow
style Base_Widget_AppCompat_ListMenuView
style Base_Widget_AppCompat_ListPopupWindow
style Base_Widget_AppCompat_ListView
style Base_Widget_AppCompat_ListView_DropDown
style Base_Widget_AppCompat_ListView_Menu
style Base_Widget_AppCompat_PopupMenu
style Base_Widget_AppCompat_PopupMenu_Overflow
style Base_Widget_AppCompat_PopupWindow
style Base_Widget_AppCompat_ProgressBar
style Base_Widget_AppCompat_ProgressBar_Horizontal
style Base_Widget_AppCompat_RatingBar
style Base_Widget_AppCompat_RatingBar_Indicator
style Base_Widget_AppCompat_RatingBar_Small
style Base_Widget_AppCompat_SearchView
style Base_Widget_AppCompat_SearchView_ActionBar
style Base_Widget_AppCompat_SeekBar
style Base_Widget_AppCompat_SeekBar_Discrete
style Base_Widget_AppCompat_Spinner
style Base_Widget_AppCompat_Spinner_Underlined
style Base_Widget_AppCompat_TextView
style Base_Widget_AppCompat_TextView_SpinnerItem
style Base_Widget_AppCompat_Toolbar
style Base_Widget_AppCompat_Toolbar_Button_Navigation
style DialogWindowTheme
style EdgeToEdgeFloatingDialogTheme
style EdgeToEdgeFloatingDialogWindowTheme
style FloatingDialogTheme
style FloatingDialogWindowTheme
style Platform_AppCompat
style Platform_AppCompat_Light
style Platform_ThemeOverlay_AppCompat
style Platform_ThemeOverlay_AppCompat_Dark
style Platform_ThemeOverlay_AppCompat_Light
style Platform_V21_AppCompat
style Platform_V21_AppCompat_Light
style Platform_V25_AppCompat
style Platform_V25_AppCompat_Light
style Platform_Widget_AppCompat_Spinner
style RtlOverlay_DialogWindowTitle_AppCompat
style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem
style RtlOverlay_Widget_AppCompat_DialogTitle_Icon
style RtlOverlay_Widget_AppCompat_PopupMenuItem
style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup
style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut
style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow
style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text
style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title
style RtlOverlay_Widget_AppCompat_Search_DropDown
style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1
style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2
style RtlOverlay_Widget_AppCompat_Search_DropDown_Query
style RtlOverlay_Widget_AppCompat_Search_DropDown_Text
style RtlOverlay_Widget_AppCompat_SearchView_MagIcon
style RtlUnderlay_Widget_AppCompat_ActionButton
style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow
style TextAppearance_AppCompat
style TextAppearance_AppCompat_Body1
style TextAppearance_AppCompat_Body2
style TextAppearance_AppCompat_Button
style TextAppearance_AppCompat_Caption
style TextAppearance_AppCompat_Display1
style TextAppearance_AppCompat_Display2
style TextAppearance_AppCompat_Display3
style TextAppearance_AppCompat_Display4
style TextAppearance_AppCompat_Headline
style TextAppearance_AppCompat_Inverse
style TextAppearance_AppCompat_Large
style TextAppearance_AppCompat_Large_Inverse
style TextAppearance_AppCompat_Light_SearchResult_Subtitle
style TextAppearance_AppCompat_Light_SearchResult_Title
style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
style TextAppearance_AppCompat_Medium
style TextAppearance_AppCompat_Medium_Inverse
style TextAppearance_AppCompat_Menu
style TextAppearance_AppCompat_SearchResult_Subtitle
style TextAppearance_AppCompat_SearchResult_Title
style TextAppearance_AppCompat_Small
style TextAppearance_AppCompat_Small_Inverse
style TextAppearance_AppCompat_Subhead
style TextAppearance_AppCompat_Subhead_Inverse
style TextAppearance_AppCompat_Title
style TextAppearance_AppCompat_Title_Inverse
style TextAppearance_AppCompat_Tooltip
style TextAppearance_AppCompat_Widget_ActionBar_Menu
style TextAppearance_AppCompat_Widget_ActionBar_Subtitle
style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
style TextAppearance_AppCompat_Widget_ActionBar_Title
style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
style TextAppearance_AppCompat_Widget_ActionMode_Subtitle
style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse
style TextAppearance_AppCompat_Widget_ActionMode_Title
style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse
style TextAppearance_AppCompat_Widget_Button
style TextAppearance_AppCompat_Widget_Button_Borderless_Colored
style TextAppearance_AppCompat_Widget_Button_Colored
style TextAppearance_AppCompat_Widget_Button_Inverse
style TextAppearance_AppCompat_Widget_DropDownItem
style TextAppearance_AppCompat_Widget_PopupMenu_Header
style TextAppearance_AppCompat_Widget_PopupMenu_Large
style TextAppearance_AppCompat_Widget_PopupMenu_Small
style TextAppearance_AppCompat_Widget_Switch
style TextAppearance_AppCompat_Widget_TextView_SpinnerItem
style TextAppearance_Compat_Notification
style TextAppearance_Compat_Notification_Info
style TextAppearance_Compat_Notification_Line2
style TextAppearance_Compat_Notification_Time
style TextAppearance_Compat_Notification_Title
style TextAppearance_Widget_AppCompat_ExpandedMenu_Item
style TextAppearance_Widget_AppCompat_Toolbar_Subtitle
style TextAppearance_Widget_AppCompat_Toolbar_Title
style Theme_AppCompat
style Theme_AppCompat_CompactMenu
style Theme_AppCompat_DayNight
style Theme_AppCompat_DayNight_DarkActionBar
style Theme_AppCompat_DayNight_Dialog
style Theme_AppCompat_DayNight_Dialog_Alert
style Theme_AppCompat_DayNight_Dialog_MinWidth
style Theme_AppCompat_DayNight_DialogWhenLarge
style Theme_AppCompat_DayNight_NoActionBar
style Theme_AppCompat_Dialog
style Theme_AppCompat_Dialog_Alert
style Theme_AppCompat_Dialog_MinWidth
style Theme_AppCompat_DialogWhenLarge
style Theme_AppCompat_Empty
style Theme_AppCompat_Light
style Theme_AppCompat_Light_DarkActionBar
style Theme_AppCompat_Light_Dialog
style Theme_AppCompat_Light_Dialog_Alert
style Theme_AppCompat_Light_Dialog_MinWidth
style Theme_AppCompat_Light_DialogWhenLarge
style Theme_AppCompat_Light_NoActionBar
style Theme_AppCompat_NoActionBar
style Theme_MountainSurvival
style ThemeOverlay_AppCompat
style ThemeOverlay_AppCompat_ActionBar
style ThemeOverlay_AppCompat_Dark
style ThemeOverlay_AppCompat_Dark_ActionBar
style ThemeOverlay_AppCompat_DayNight
style ThemeOverlay_AppCompat_DayNight_ActionBar
style ThemeOverlay_AppCompat_Dialog
style ThemeOverlay_AppCompat_Dialog_Alert
style ThemeOverlay_AppCompat_Light
style Widget_AppCompat_ActionBar
style Widget_AppCompat_ActionBar_Solid
style Widget_AppCompat_ActionBar_TabBar
style Widget_AppCompat_ActionBar_TabText
style Widget_AppCompat_ActionBar_TabView
style Widget_AppCompat_ActionButton
style Widget_AppCompat_ActionButton_CloseMode
style Widget_AppCompat_ActionButton_Overflow
style Widget_AppCompat_ActionMode
style Widget_AppCompat_ActivityChooserView
style Widget_AppCompat_AutoCompleteTextView
style Widget_AppCompat_Button
style Widget_AppCompat_Button_Borderless
style Widget_AppCompat_Button_Borderless_Colored
style Widget_AppCompat_Button_ButtonBar_AlertDialog
style Widget_AppCompat_Button_Colored
style Widget_AppCompat_Button_Small
style Widget_AppCompat_ButtonBar
style Widget_AppCompat_ButtonBar_AlertDialog
style Widget_AppCompat_CompoundButton_CheckBox
style Widget_AppCompat_CompoundButton_RadioButton
style Widget_AppCompat_CompoundButton_Switch
style Widget_AppCompat_DrawerArrowToggle
style Widget_AppCompat_DropDownItem_Spinner
style Widget_AppCompat_EditText
style Widget_AppCompat_ImageButton
style Widget_AppCompat_Light_ActionBar
style Widget_AppCompat_Light_ActionBar_Solid
style Widget_AppCompat_Light_ActionBar_Solid_Inverse
style Widget_AppCompat_Light_ActionBar_TabBar
style Widget_AppCompat_Light_ActionBar_TabBar_Inverse
style Widget_AppCompat_Light_ActionBar_TabText
style Widget_AppCompat_Light_ActionBar_TabText_Inverse
style Widget_AppCompat_Light_ActionBar_TabView
style Widget_AppCompat_Light_ActionBar_TabView_Inverse
style Widget_AppCompat_Light_ActionButton
style Widget_AppCompat_Light_ActionButton_CloseMode
style Widget_AppCompat_Light_ActionButton_Overflow
style Widget_AppCompat_Light_ActionMode_Inverse
style Widget_AppCompat_Light_ActivityChooserView
style Widget_AppCompat_Light_AutoCompleteTextView
style Widget_AppCompat_Light_DropDownItem_Spinner
style Widget_AppCompat_Light_ListPopupWindow
style Widget_AppCompat_Light_ListView_DropDown
style Widget_AppCompat_Light_PopupMenu
style Widget_AppCompat_Light_PopupMenu_Overflow
style Widget_AppCompat_Light_SearchView
style Widget_AppCompat_Light_Spinner_DropDown_ActionBar
style Widget_AppCompat_ListMenuView
style Widget_AppCompat_ListPopupWindow
style Widget_AppCompat_ListView
style Widget_AppCompat_ListView_DropDown
style Widget_AppCompat_ListView_Menu
style Widget_AppCompat_PopupMenu
style Widget_AppCompat_PopupMenu_Overflow
style Widget_AppCompat_PopupWindow
style Widget_AppCompat_ProgressBar
style Widget_AppCompat_ProgressBar_Horizontal
style Widget_AppCompat_RatingBar
style Widget_AppCompat_RatingBar_Indicator
style Widget_AppCompat_RatingBar_Small
style Widget_AppCompat_SearchView
style Widget_AppCompat_SearchView_ActionBar
style Widget_AppCompat_SeekBar
style Widget_AppCompat_SeekBar_Discrete
style Widget_AppCompat_Spinner
style Widget_AppCompat_Spinner_DropDown
style Widget_AppCompat_Spinner_DropDown_ActionBar
style Widget_AppCompat_Spinner_Underlined
style Widget_AppCompat_TextView
style Widget_AppCompat_TextView_SpinnerItem
style Widget_AppCompat_Toolbar
style Widget_AppCompat_Toolbar_Button_Navigation
style Widget_Compat_NotificationActionContainer
style Widget_Compat_NotificationActionText
styleable ActionBar background backgroundSplit backgroundStacked contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation customNavigationLayout displayOptions divider elevation height hideOnContentScroll homeAsUpIndicator homeLayout icon indeterminateProgressStyle itemPadding logo navigationMode popupTheme progressBarPadding progressBarStyle subtitle subtitleTextStyle title titleTextStyle
styleable ActionBarLayout android_layout_gravity
styleable ActionMenuItemView android_minWidth
styleable ActionMenuView
styleable ActionMode background backgroundSplit closeItemLayout height subtitleTextStyle titleTextStyle
styleable ActivityChooserView expandActivityOverflowButtonDrawable initialActivityCount
styleable AlertDialog android_layout buttonIconDimen buttonPanelSideLayout listItemLayout listLayout multiChoiceItemLayout showTitle singleChoiceItemLayout
styleable AnimatedStateListDrawableCompat android_dither android_visible android_variablePadding android_constantSize android_enterFadeDuration android_exitFadeDuration
styleable AnimatedStateListDrawableItem android_id android_drawable
styleable AnimatedStateListDrawableTransition android_drawable android_toId android_fromId android_reversible
styleable AppCompatEmojiHelper
styleable AppCompatImageView android_src srcCompat tint tintMode
styleable AppCompatSeekBar android_thumb tickMark tickMarkTint tickMarkTintMode
styleable AppCompatTextHelper android_textAppearance android_drawableTop android_drawableBottom android_drawableLeft android_drawableRight android_drawableStart android_drawableEnd
styleable AppCompatTextView android_textAppearance autoSizeMaxTextSize autoSizeMinTextSize autoSizePresetSizes autoSizeStepGranularity autoSizeTextType drawableBottomCompat drawableEndCompat drawableLeftCompat drawableRightCompat drawableStartCompat drawableTint drawableTintMode drawableTopCompat emojiCompatEnabled firstBaselineToTopHeight fontFamily fontVariationSettings lastBaselineToBottomHeight lineHeight textAllCaps textLocale
styleable AppCompatTheme android_windowIsFloating android_windowAnimationStyle actionBarDivider actionBarItemBackground actionBarPopupTheme actionBarSize actionBarSplitStyle actionBarStyle actionBarTabBarStyle actionBarTabStyle actionBarTabTextStyle actionBarTheme actionBarWidgetTheme actionButtonStyle actionDropDownStyle actionMenuTextAppearance actionMenuTextColor actionModeBackground actionModeCloseButtonStyle actionModeCloseContentDescription actionModeCloseDrawable actionModeCopyDrawable actionModeCutDrawable actionModeFindDrawable actionModePasteDrawable actionModePopupWindowStyle actionModeSelectAllDrawable actionModeShareDrawable actionModeSplitBackground actionModeStyle actionModeTheme actionModeWebSearchDrawable actionOverflowButtonStyle actionOverflowMenuStyle activityChooserViewStyle alertDialogButtonGroupStyle alertDialogCenterButtons alertDialogStyle alertDialogTheme autoCompleteTextViewStyle borderlessButtonStyle buttonBarButtonStyle buttonBarNegativeButtonStyle buttonBarNeutralButtonStyle buttonBarPositiveButtonStyle buttonBarStyle buttonStyle buttonStyleSmall checkboxStyle checkedTextViewStyle colorAccent colorBackgroundFloating colorButtonNormal colorControlActivated colorControlHighlight colorControlNormal colorError colorPrimary colorPrimaryDark colorSwitchThumbNormal controlBackground dialogCornerRadius dialogPreferredPadding dialogTheme dividerHorizontal dividerVertical dropDownListViewStyle dropdownListPreferredItemHeight editTextBackground editTextColor editTextStyle homeAsUpIndicator imageButtonStyle listChoiceBackgroundIndicator listChoiceIndicatorMultipleAnimated listChoiceIndicatorSingleAnimated listDividerAlertDialog listMenuViewStyle listPopupWindowStyle listPreferredItemHeight listPreferredItemHeightLarge listPreferredItemHeightSmall listPreferredItemPaddingEnd listPreferredItemPaddingLeft listPreferredItemPaddingRight listPreferredItemPaddingStart panelBackground panelMenuListTheme panelMenuListWidth popupMenuStyle popupWindowStyle radioButtonStyle ratingBarStyle ratingBarStyleIndicator ratingBarStyleSmall searchViewStyle seekBarStyle selectableItemBackground selectableItemBackgroundBorderless spinnerDropDownItemStyle spinnerStyle switchStyle textAppearanceLargePopupMenu textAppearanceListItem textAppearanceListItemSecondary textAppearanceListItemSmall textAppearancePopupMenuHeader textAppearanceSearchResultSubtitle textAppearanceSearchResultTitle textAppearanceSmallPopupMenu textColorAlertDialogListItem textColorSearchUrl toolbarNavigationButtonStyle toolbarStyle tooltipForegroundColor tooltipFrameBackground viewInflaterClass windowActionBar windowActionBarOverlay windowActionModeOverlay windowFixedHeightMajor windowFixedHeightMinor windowFixedWidthMajor windowFixedWidthMinor windowMinWidthMajor windowMinWidthMinor windowNoTitle
styleable ButtonBarLayout allowStacking
styleable Capability queryPatterns shortcutMatchRequired
styleable Carousel carousel_backwardTransition carousel_emptyViewsBehavior carousel_firstView carousel_forwardTransition carousel_infinite carousel_nextState carousel_previousState carousel_touchUpMode carousel_touchUp_dampeningFactor carousel_touchUp_velocityThreshold
styleable CheckedTextView android_checkMark checkMarkCompat checkMarkTint checkMarkTintMode
styleable ColorStateListItem android_color android_alpha android_lStar alpha lStar
styleable CompoundButton android_button buttonCompat buttonTint buttonTintMode
styleable Constraint android_orientation android_id android_visibility android_layout_width android_layout_height android_layout_marginLeft android_layout_marginTop android_layout_marginRight android_layout_marginBottom android_maxWidth android_maxHeight android_minWidth android_minHeight android_alpha android_transformPivotX android_transformPivotY android_translationX android_translationY android_scaleX android_scaleY android_rotation android_rotationX android_rotationY android_layout_marginStart android_layout_marginEnd android_translationZ android_elevation animateCircleAngleTo animateRelativeTo barrierAllowsGoneWidgets barrierDirection barrierMargin chainUseRtl constraint_referenced_ids constraint_referenced_tags drawPath flow_firstHorizontalBias flow_firstHorizontalStyle flow_firstVerticalBias flow_firstVerticalStyle flow_horizontalAlign flow_horizontalBias flow_horizontalGap flow_horizontalStyle flow_lastHorizontalBias flow_lastHorizontalStyle flow_lastVerticalBias flow_lastVerticalStyle flow_maxElementsWrap flow_verticalAlign flow_verticalBias flow_verticalGap flow_verticalStyle flow_wrapMode guidelineUseRtl layout_constrainedHeight layout_constrainedWidth layout_constraintBaseline_creator layout_constraintBaseline_toBaselineOf layout_constraintBaseline_toBottomOf layout_constraintBaseline_toTopOf layout_constraintBottom_creator layout_constraintBottom_toBottomOf layout_constraintBottom_toTopOf layout_constraintCircle layout_constraintCircleAngle layout_constraintCircleRadius layout_constraintDimensionRatio layout_constraintEnd_toEndOf layout_constraintEnd_toStartOf layout_constraintGuide_begin layout_constraintGuide_end layout_constraintGuide_percent layout_constraintHeight layout_constraintHeight_default layout_constraintHeight_max layout_constraintHeight_min layout_constraintHeight_percent layout_constraintHorizontal_bias layout_constraintHorizontal_chainStyle layout_constraintHorizontal_weight layout_constraintLeft_creator layout_constraintLeft_toLeftOf layout_constraintLeft_toRightOf layout_constraintRight_creator layout_constraintRight_toLeftOf layout_constraintRight_toRightOf layout_constraintStart_toEndOf layout_constraintStart_toStartOf layout_constraintTag layout_constraintTop_creator layout_constraintTop_toBottomOf layout_constraintTop_toTopOf layout_constraintVertical_bias layout_constraintVertical_chainStyle layout_constraintVertical_weight layout_constraintWidth layout_constraintWidth_default layout_constraintWidth_max layout_constraintWidth_min layout_constraintWidth_percent layout_editor_absoluteX layout_editor_absoluteY layout_goneMarginBaseline layout_goneMarginBottom layout_goneMarginEnd layout_goneMarginLeft layout_goneMarginRight layout_goneMarginStart layout_goneMarginTop layout_marginBaseline layout_wrapBehaviorInParent motionProgress motionStagger pathMotionArc pivotAnchor polarRelativeTo quantizeMotionInterpolator quantizeMotionPhase quantizeMotionSteps transformPivotTarget transitionEasing transitionPathRotate visibilityMode
styleable ConstraintLayout_Layout android_orientation android_padding android_paddingLeft android_paddingTop android_paddingRight android_paddingBottom android_visibility android_layout_width android_layout_height android_layout_margin android_layout_marginLeft android_layout_marginTop android_layout_marginRight android_layout_marginBottom android_maxWidth android_maxHeight android_minWidth android_minHeight android_paddingStart android_paddingEnd android_layout_marginStart android_layout_marginEnd android_elevation android_layout_marginHorizontal android_layout_marginVertical barrierAllowsGoneWidgets barrierDirection barrierMargin chainUseRtl circularflow_angles circularflow_defaultAngle circularflow_defaultRadius circularflow_radiusInDP circularflow_viewCenter constraintSet constraint_referenced_ids constraint_referenced_tags flow_firstHorizontalBias flow_firstHorizontalStyle flow_firstVerticalBias flow_firstVerticalStyle flow_horizontalAlign flow_horizontalBias flow_horizontalGap flow_horizontalStyle flow_lastHorizontalBias flow_lastHorizontalStyle flow_lastVerticalBias flow_lastVerticalStyle flow_maxElementsWrap flow_verticalAlign flow_verticalBias flow_verticalGap flow_verticalStyle flow_wrapMode guidelineUseRtl layoutDescription layout_constrainedHeight layout_constrainedWidth layout_constraintBaseline_creator layout_constraintBaseline_toBaselineOf layout_constraintBaseline_toBottomOf layout_constraintBaseline_toTopOf layout_constraintBottom_creator layout_constraintBottom_toBottomOf layout_constraintBottom_toTopOf layout_constraintCircle layout_constraintCircleAngle layout_constraintCircleRadius layout_constraintDimensionRatio layout_constraintEnd_toEndOf layout_constraintEnd_toStartOf layout_constraintGuide_begin layout_constraintGuide_end layout_constraintGuide_percent layout_constraintHeight layout_constraintHeight_default layout_constraintHeight_max layout_constraintHeight_min layout_constraintHeight_percent layout_constraintHorizontal_bias layout_constraintHorizontal_chainStyle layout_constraintHorizontal_weight layout_constraintLeft_creator layout_constraintLeft_toLeftOf layout_constraintLeft_toRightOf layout_constraintRight_creator layout_constraintRight_toLeftOf layout_constraintRight_toRightOf layout_constraintStart_toEndOf layout_constraintStart_toStartOf layout_constraintTag layout_constraintTop_creator layout_constraintTop_toBottomOf layout_constraintTop_toTopOf layout_constraintVertical_bias layout_constraintVertical_chainStyle layout_constraintVertical_weight layout_constraintWidth layout_constraintWidth_default layout_constraintWidth_max layout_constraintWidth_min layout_constraintWidth_percent layout_editor_absoluteX layout_editor_absoluteY layout_goneMarginBaseline layout_goneMarginBottom layout_goneMarginEnd layout_goneMarginLeft layout_goneMarginRight layout_goneMarginStart layout_goneMarginTop layout_marginBaseline layout_optimizationLevel layout_wrapBehaviorInParent
styleable ConstraintLayout_ReactiveGuide reactiveGuide_animateChange reactiveGuide_applyToAllConstraintSets reactiveGuide_applyToConstraintSet reactiveGuide_valueId
styleable ConstraintLayout_placeholder content placeholder_emptyVisibility
styleable ConstraintOverride android_orientation android_id android_visibility android_layout_width android_layout_height android_layout_marginLeft android_layout_marginTop android_layout_marginRight android_layout_marginBottom android_maxWidth android_maxHeight android_minWidth android_minHeight android_alpha android_transformPivotX android_transformPivotY android_translationX android_translationY android_scaleX android_scaleY android_rotation android_rotationX android_rotationY android_layout_marginStart android_layout_marginEnd android_translationZ android_elevation animateCircleAngleTo animateRelativeTo barrierAllowsGoneWidgets barrierDirection barrierMargin chainUseRtl constraint_referenced_ids drawPath flow_firstHorizontalBias flow_firstHorizontalStyle flow_firstVerticalBias flow_firstVerticalStyle flow_horizontalAlign flow_horizontalBias flow_horizontalGap flow_horizontalStyle flow_lastHorizontalBias flow_lastHorizontalStyle flow_lastVerticalBias flow_lastVerticalStyle flow_maxElementsWrap flow_verticalAlign flow_verticalBias flow_verticalGap flow_verticalStyle flow_wrapMode guidelineUseRtl layout_constrainedHeight layout_constrainedWidth layout_constraintBaseline_creator layout_constraintBottom_creator layout_constraintCircleAngle layout_constraintCircleRadius layout_constraintDimensionRatio layout_constraintGuide_begin layout_constraintGuide_end layout_constraintGuide_percent layout_constraintHeight layout_constraintHeight_default layout_constraintHeight_max layout_constraintHeight_min layout_constraintHeight_percent layout_constraintHorizontal_bias layout_constraintHorizontal_chainStyle layout_constraintHorizontal_weight layout_constraintLeft_creator layout_constraintRight_creator layout_constraintTag layout_constraintTop_creator layout_constraintVertical_bias layout_constraintVertical_chainStyle layout_constraintVertical_weight layout_constraintWidth layout_constraintWidth_default layout_constraintWidth_max layout_constraintWidth_min layout_constraintWidth_percent layout_editor_absoluteX layout_editor_absoluteY layout_goneMarginBaseline layout_goneMarginBottom layout_goneMarginEnd layout_goneMarginLeft layout_goneMarginRight layout_goneMarginStart layout_goneMarginTop layout_marginBaseline layout_wrapBehaviorInParent motionProgress motionStagger motionTarget pathMotionArc pivotAnchor polarRelativeTo quantizeMotionInterpolator quantizeMotionPhase quantizeMotionSteps transformPivotTarget transitionEasing transitionPathRotate visibilityMode
styleable ConstraintSet android_orientation android_id android_visibility android_layout_width android_layout_height android_layout_marginLeft android_layout_marginTop android_layout_marginRight android_layout_marginBottom android_maxWidth android_maxHeight android_minWidth android_minHeight android_pivotX android_pivotY android_alpha android_transformPivotX android_transformPivotY android_translationX android_translationY android_scaleX android_scaleY android_rotation android_rotationX android_rotationY android_layout_marginStart android_layout_marginEnd android_translationZ android_elevation animateCircleAngleTo animateRelativeTo barrierAllowsGoneWidgets barrierDirection barrierMargin chainUseRtl constraintRotate constraint_referenced_ids constraint_referenced_tags deriveConstraintsFrom drawPath flow_firstHorizontalBias flow_firstHorizontalStyle flow_firstVerticalBias flow_firstVerticalStyle flow_horizontalAlign flow_horizontalBias flow_horizontalGap flow_horizontalStyle flow_lastHorizontalBias flow_lastHorizontalStyle flow_lastVerticalBias flow_lastVerticalStyle flow_maxElementsWrap flow_verticalAlign flow_verticalBias flow_verticalGap flow_verticalStyle flow_wrapMode guidelineUseRtl layout_constrainedHeight layout_constrainedWidth layout_constraintBaseline_creator layout_constraintBaseline_toBaselineOf layout_constraintBaseline_toBottomOf layout_constraintBaseline_toTopOf layout_constraintBottom_creator layout_constraintBottom_toBottomOf layout_constraintBottom_toTopOf layout_constraintCircle layout_constraintCircleAngle layout_constraintCircleRadius layout_constraintDimensionRatio layout_constraintEnd_toEndOf layout_constraintEnd_toStartOf layout_constraintGuide_begin layout_constraintGuide_end layout_constraintGuide_percent layout_constraintHeight_default layout_constraintHeight_max layout_constraintHeight_min layout_constraintHeight_percent layout_constraintHorizontal_bias layout_constraintHorizontal_chainStyle layout_constraintHorizontal_weight layout_constraintLeft_creator layout_constraintLeft_toLeftOf layout_constraintLeft_toRightOf layout_constraintRight_creator layout_constraintRight_toLeftOf layout_constraintRight_toRightOf layout_constraintStart_toEndOf layout_constraintStart_toStartOf layout_constraintTag layout_constraintTop_creator layout_constraintTop_toBottomOf layout_constraintTop_toTopOf layout_constraintVertical_bias layout_constraintVertical_chainStyle layout_constraintVertical_weight layout_constraintWidth_default layout_constraintWidth_max layout_constraintWidth_min layout_constraintWidth_percent layout_editor_absoluteX layout_editor_absoluteY layout_goneMarginBaseline layout_goneMarginBottom layout_goneMarginEnd layout_goneMarginLeft layout_goneMarginRight layout_goneMarginStart layout_goneMarginTop layout_marginBaseline layout_wrapBehaviorInParent motionProgress motionStagger pathMotionArc pivotAnchor polarRelativeTo quantizeMotionSteps transitionEasing transitionPathRotate
styleable CustomAttribute attributeName customBoolean customColorDrawableValue customColorValue customDimension customFloatValue customIntegerValue customPixelDimension customReference customStringValue methodName
styleable DrawerArrowToggle arrowHeadLength arrowShaftLength barLength color drawableSize gapBetweenBars spinBars thickness
styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery fontProviderSystemFontFamily
styleable FontFamilyFont android_font android_fontWeight android_fontStyle android_ttcIndex android_fontVariationSettings font fontStyle fontVariationSettings fontWeight ttcIndex
styleable Fragment android_name android_id android_tag
styleable FragmentContainerView android_name android_tag
styleable GradientColor android_startColor android_endColor android_type android_centerX android_centerY android_gradientRadius android_tileMode android_centerColor android_startX android_startY android_endX android_endY
styleable GradientColorItem android_color android_offset
styleable ImageFilterView altSrc blendSrc brightness contrast crossfade imagePanX imagePanY imageRotate imageZoom overlay round roundPercent saturation warmth
styleable KeyAttribute android_alpha android_transformPivotX android_transformPivotY android_translationX android_translationY android_scaleX android_scaleY android_rotation android_rotationX android_rotationY android_translationZ android_elevation curveFit framePosition motionProgress motionTarget transformPivotTarget transitionEasing transitionPathRotate
styleable KeyCycle android_alpha android_translationX android_translationY android_scaleX android_scaleY android_rotation android_rotationX android_rotationY android_translationZ android_elevation curveFit framePosition motionProgress motionTarget transitionEasing transitionPathRotate waveOffset wavePeriod wavePhase waveShape waveVariesBy
styleable KeyFrame
styleable KeyFramesAcceleration
styleable KeyFramesVelocity
styleable KeyPosition curveFit drawPath framePosition keyPositionType motionTarget pathMotionArc percentHeight percentWidth percentX percentY sizePercent transitionEasing
styleable KeyTimeCycle android_alpha android_translationX android_translationY android_scaleX android_scaleY android_rotation android_rotationX android_rotationY android_translationZ android_elevation curveFit framePosition motionProgress motionTarget transitionEasing transitionPathRotate waveDecay waveOffset wavePeriod wavePhase waveShape
styleable KeyTrigger framePosition motionTarget motion_postLayoutCollision motion_triggerOnCollision onCross onNegativeCross onPositiveCross triggerId triggerReceiver triggerSlack viewTransitionOnCross viewTransitionOnNegativeCross viewTransitionOnPositiveCross
styleable Layout android_orientation android_layout_width android_layout_height android_layout_marginLeft android_layout_marginTop android_layout_marginRight android_layout_marginBottom android_layout_marginStart android_layout_marginEnd barrierAllowsGoneWidgets barrierDirection barrierMargin chainUseRtl constraint_referenced_ids constraint_referenced_tags guidelineUseRtl layout_constrainedHeight layout_constrainedWidth layout_constraintBaseline_creator layout_constraintBaseline_toBaselineOf layout_constraintBaseline_toBottomOf layout_constraintBaseline_toTopOf layout_constraintBottom_creator layout_constraintBottom_toBottomOf layout_constraintBottom_toTopOf layout_constraintCircle layout_constraintCircleAngle layout_constraintCircleRadius layout_constraintDimensionRatio layout_constraintEnd_toEndOf layout_constraintEnd_toStartOf layout_constraintGuide_begin layout_constraintGuide_end layout_constraintGuide_percent layout_constraintHeight layout_constraintHeight_default layout_constraintHeight_max layout_constraintHeight_min layout_constraintHeight_percent layout_constraintHorizontal_bias layout_constraintHorizontal_chainStyle layout_constraintHorizontal_weight layout_constraintLeft_creator layout_constraintLeft_toLeftOf layout_constraintLeft_toRightOf layout_constraintRight_creator layout_constraintRight_toLeftOf layout_constraintRight_toRightOf layout_constraintStart_toEndOf layout_constraintStart_toStartOf layout_constraintTop_creator layout_constraintTop_toBottomOf layout_constraintTop_toTopOf layout_constraintVertical_bias layout_constraintVertical_chainStyle layout_constraintVertical_weight layout_constraintWidth layout_constraintWidth_default layout_constraintWidth_max layout_constraintWidth_min layout_constraintWidth_percent layout_editor_absoluteX layout_editor_absoluteY layout_goneMarginBaseline layout_goneMarginBottom layout_goneMarginEnd layout_goneMarginLeft layout_goneMarginRight layout_goneMarginStart layout_goneMarginTop layout_marginBaseline layout_wrapBehaviorInParent maxHeight maxWidth minHeight minWidth
styleable LinearLayoutCompat android_gravity android_orientation android_baselineAligned android_baselineAlignedChildIndex android_weightSum divider dividerPadding measureWithLargestChild showDividers
styleable LinearLayoutCompat_Layout android_layout_gravity android_layout_width android_layout_height android_layout_weight
styleable ListPopupWindow android_dropDownHorizontalOffset android_dropDownVerticalOffset
styleable MenuGroup android_enabled android_id android_visible android_menuCategory android_orderInCategory android_checkableBehavior
styleable MenuItem android_icon android_enabled android_id android_checked android_visible android_menuCategory android_orderInCategory android_title android_titleCondensed android_alphabeticShortcut android_numericShortcut android_checkable android_onClick actionLayout actionProviderClass actionViewClass alphabeticModifiers contentDescription iconTint iconTintMode numericModifiers showAsAction tooltipText
styleable MenuView android_windowAnimationStyle android_itemTextAppearance android_horizontalDivider android_verticalDivider android_headerBackground android_itemBackground android_itemIconDisabledAlpha preserveIconSpacing subMenuArrow
styleable MockView mock_diagonalsColor mock_label mock_labelBackgroundColor mock_labelColor mock_showDiagonals mock_showLabel
styleable Motion animateCircleAngleTo animateRelativeTo drawPath motionPathRotate motionStagger pathMotionArc quantizeMotionInterpolator quantizeMotionPhase quantizeMotionSteps transitionEasing
styleable MotionEffect motionEffect_alpha motionEffect_end motionEffect_move motionEffect_start motionEffect_strict motionEffect_translationX motionEffect_translationY motionEffect_viewTransition
styleable MotionHelper onHide onShow
styleable MotionLabel android_textSize android_typeface android_textStyle android_textColor android_gravity android_text android_shadowRadius android_fontFamily android_autoSizeTextType borderRound borderRoundPercent scaleFromTextSize textBackground textBackgroundPanX textBackgroundPanY textBackgroundRotate textBackgroundZoom textOutlineColor textOutlineThickness textPanX textPanY textureBlurFactor textureEffect textureHeight textureWidth
styleable MotionLayout applyMotionScene currentState layoutDescription motionDebug motionProgress showPaths
styleable MotionScene defaultDuration layoutDuringTransition
styleable MotionTelltales telltales_tailColor telltales_tailScale telltales_velocityMode
styleable OnClick clickAction targetId
styleable OnSwipe autoCompleteMode dragDirection dragScale dragThreshold limitBoundsTo maxAcceleration maxVelocity moveWhenScrollAtTop nestedScrollFlags onTouchUp rotationCenterId springBoundary springDamping springMass springStiffness springStopThreshold touchAnchorId touchAnchorSide touchRegionId
styleable PopupWindow android_popupBackground android_popupAnimationStyle overlapAnchor
styleable PopupWindowBackgroundState state_above_anchor
styleable PropertySet android_visibility android_alpha layout_constraintTag motionProgress visibilityMode
styleable RecycleListView paddingBottomNoButtons paddingTopNoTitle
styleable SearchView android_focusable android_maxWidth android_inputType android_imeOptions closeIcon commitIcon defaultQueryHint goIcon iconifiedByDefault layout queryBackground queryHint searchHintIcon searchIcon submitBackground suggestionRowLayout voiceIcon
styleable Spinner android_entries android_popupBackground android_prompt android_dropDownWidth popupTheme
styleable State android_id constraints
styleable StateListDrawable android_dither android_visible android_variablePadding android_constantSize android_enterFadeDuration android_exitFadeDuration
styleable StateListDrawableItem android_drawable
styleable StateSet defaultState
styleable SwitchCompat android_textOn android_textOff android_thumb showText splitTrack switchMinWidth switchPadding switchTextAppearance thumbTextPadding thumbTint thumbTintMode track trackTint trackTintMode
styleable TextAppearance android_textSize android_typeface android_textStyle android_textColor android_textColorHint android_textColorLink android_shadowColor android_shadowDx android_shadowDy android_shadowRadius android_fontFamily android_textFontWeight fontFamily fontVariationSettings textAllCaps textLocale
styleable TextEffects android_textSize android_typeface android_textStyle android_text android_shadowColor android_shadowDx android_shadowDy android_shadowRadius android_fontFamily borderRound borderRoundPercent textFillColor textOutlineColor textOutlineThickness
styleable Toolbar android_gravity android_minHeight buttonGravity collapseContentDescription collapseIcon contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation logo logoDescription maxButtonHeight menu navigationContentDescription navigationIcon popupTheme subtitle subtitleTextAppearance subtitleTextColor title titleMargin titleMarginBottom titleMarginEnd titleMarginStart titleMarginTop titleMargins titleTextAppearance titleTextColor
styleable Transform android_transformPivotX android_transformPivotY android_translationX android_translationY android_scaleX android_scaleY android_rotation android_rotationX android_rotationY android_translationZ android_elevation transformPivotTarget
styleable Transition android_id autoTransition constraintSetEnd constraintSetStart duration layoutDuringTransition motionInterpolator pathMotionArc staggered transitionDisable transitionFlags
styleable Variant constraints region_heightLessThan region_heightMoreThan region_widthLessThan region_widthMoreThan
styleable View android_theme android_focusable paddingEnd paddingStart theme
styleable ViewBackgroundHelper android_background backgroundTint backgroundTintMode
styleable ViewStubCompat android_id android_layout android_inflatedId
styleable ViewTransition android_id SharedValue SharedValueId clearsTag duration ifTagNotSet ifTagSet motionInterpolator motionTarget onStateTransition pathMotionArc setsTag transitionDisable upDuration viewTransitionMode
styleable include constraintSet
xml backup_rules
xml data_extraction_rules
