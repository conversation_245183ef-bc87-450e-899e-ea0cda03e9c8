  Activity android.app  ActivityMainBinding android.app.Activity  ActivityStartBinding android.app.Activity  Choice android.app.Activity  Double android.app.Activity  	Exception android.app.Activity  Float android.app.Activity  
GameEngine android.app.Activity  GameEventUtils android.app.Activity  GameLoopTest android.app.Activity  GameManager android.app.Activity  GameUIState android.app.Activity  Int android.app.Activity  Intent android.app.Activity  Log android.app.Activity  Long android.app.Activity  MainActivity android.app.Activity  Pair android.app.Activity  String android.app.Activity  TAG android.app.Activity  Triple android.app.Activity  View android.app.Activity  applyChoice android.app.Activity  
checkGameOver android.app.Activity  
component1 android.app.Activity  
component2 android.app.Activity  contains android.app.Activity  	emptyList android.app.Activity  emptyMap android.app.Activity  filter android.app.Activity  finish android.app.Activity  forEachIndexed android.app.Activity  getEvent android.app.Activity  getEventMapStatistics android.app.Activity  getGameOverReason android.app.Activity  initializeGame android.app.Activity  isChoiceAvailable android.app.Activity  
isNotEmpty android.app.Activity  
isNullOrEmpty android.app.Activity  	isVictory android.app.Activity  java android.app.Activity  joinToString android.app.Activity  layoutInflater android.app.Activity  let android.app.Activity  listOf android.app.Activity  
loadEvents android.app.Activity  mapOf android.app.Activity  
mutableListOf android.app.Activity  mutableMapOf android.app.Activity  onCreate android.app.Activity  performNightPhase android.app.Activity  	resetGame android.app.Activity  run android.app.Activity  selectRandomEvent android.app.Activity  set android.app.Activity  split android.app.Activity  
startActivity android.app.Activity  take android.app.Activity  testGameLoop android.app.Activity  to android.app.Activity  toIntOrNull android.app.Activity  trim android.app.Activity  
trimIndent android.app.Activity  validateEventMap android.app.Activity  Context android.content  Intent android.content  ActivityMainBinding android.content.Context  ActivityStartBinding android.content.Context  Choice android.content.Context  Double android.content.Context  	Exception android.content.Context  Float android.content.Context  
GameEngine android.content.Context  GameEventUtils android.content.Context  GameLoopTest android.content.Context  GameManager android.content.Context  GameUIState android.content.Context  Int android.content.Context  Intent android.content.Context  Log android.content.Context  Long android.content.Context  MainActivity android.content.Context  Pair android.content.Context  String android.content.Context  TAG android.content.Context  Triple android.content.Context  View android.content.Context  applyChoice android.content.Context  assets android.content.Context  
checkGameOver android.content.Context  
component1 android.content.Context  
component2 android.content.Context  contains android.content.Context  	emptyList android.content.Context  emptyMap android.content.Context  filter android.content.Context  forEachIndexed android.content.Context  getEvent android.content.Context  getEventMapStatistics android.content.Context  getGameOverReason android.content.Context  initializeGame android.content.Context  isChoiceAvailable android.content.Context  
isNotEmpty android.content.Context  
isNullOrEmpty android.content.Context  	isVictory android.content.Context  java android.content.Context  joinToString android.content.Context  let android.content.Context  listOf android.content.Context  
loadEvents android.content.Context  mapOf android.content.Context  
mutableListOf android.content.Context  mutableMapOf android.content.Context  performNightPhase android.content.Context  	resetGame android.content.Context  run android.content.Context  selectRandomEvent android.content.Context  set android.content.Context  split android.content.Context  take android.content.Context  testGameLoop android.content.Context  to android.content.Context  toIntOrNull android.content.Context  trim android.content.Context  
trimIndent android.content.Context  validateEventMap android.content.Context  ActivityMainBinding android.content.ContextWrapper  ActivityStartBinding android.content.ContextWrapper  Choice android.content.ContextWrapper  Double android.content.ContextWrapper  	Exception android.content.ContextWrapper  Float android.content.ContextWrapper  
GameEngine android.content.ContextWrapper  GameEventUtils android.content.ContextWrapper  GameLoopTest android.content.ContextWrapper  GameManager android.content.ContextWrapper  GameUIState android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  Log android.content.ContextWrapper  Long android.content.ContextWrapper  MainActivity android.content.ContextWrapper  Pair android.content.ContextWrapper  String android.content.ContextWrapper  TAG android.content.ContextWrapper  Triple android.content.ContextWrapper  View android.content.ContextWrapper  applyChoice android.content.ContextWrapper  
checkGameOver android.content.ContextWrapper  
component1 android.content.ContextWrapper  
component2 android.content.ContextWrapper  contains android.content.ContextWrapper  	emptyList android.content.ContextWrapper  emptyMap android.content.ContextWrapper  filter android.content.ContextWrapper  forEachIndexed android.content.ContextWrapper  getEvent android.content.ContextWrapper  getEventMapStatistics android.content.ContextWrapper  getGameOverReason android.content.ContextWrapper  initializeGame android.content.ContextWrapper  isChoiceAvailable android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isNullOrEmpty android.content.ContextWrapper  	isVictory android.content.ContextWrapper  java android.content.ContextWrapper  joinToString android.content.ContextWrapper  let android.content.ContextWrapper  listOf android.content.ContextWrapper  
loadEvents android.content.ContextWrapper  mapOf android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  mutableMapOf android.content.ContextWrapper  performNightPhase android.content.ContextWrapper  	resetGame android.content.ContextWrapper  run android.content.ContextWrapper  selectRandomEvent android.content.ContextWrapper  set android.content.ContextWrapper  split android.content.ContextWrapper  take android.content.ContextWrapper  testGameLoop android.content.ContextWrapper  to android.content.ContextWrapper  toIntOrNull android.content.ContextWrapper  trim android.content.ContextWrapper  
trimIndent android.content.ContextWrapper  validateEventMap android.content.ContextWrapper  open  android.content.res.AssetManager  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  LayoutInflater android.view  View android.view  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityStartBinding  android.view.ContextThemeWrapper  Choice  android.view.ContextThemeWrapper  Double  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  Float  android.view.ContextThemeWrapper  
GameEngine  android.view.ContextThemeWrapper  GameEventUtils  android.view.ContextThemeWrapper  GameLoopTest  android.view.ContextThemeWrapper  GameManager  android.view.ContextThemeWrapper  GameUIState  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Long  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  Pair  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  Triple  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  applyChoice  android.view.ContextThemeWrapper  
checkGameOver  android.view.ContextThemeWrapper  
component1  android.view.ContextThemeWrapper  
component2  android.view.ContextThemeWrapper  contains  android.view.ContextThemeWrapper  	emptyList  android.view.ContextThemeWrapper  emptyMap  android.view.ContextThemeWrapper  filter  android.view.ContextThemeWrapper  forEachIndexed  android.view.ContextThemeWrapper  getEvent  android.view.ContextThemeWrapper  getEventMapStatistics  android.view.ContextThemeWrapper  getGameOverReason  android.view.ContextThemeWrapper  initializeGame  android.view.ContextThemeWrapper  isChoiceAvailable  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  
isNullOrEmpty  android.view.ContextThemeWrapper  	isVictory  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  joinToString  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  
loadEvents  android.view.ContextThemeWrapper  mapOf  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  mutableMapOf  android.view.ContextThemeWrapper  performNightPhase  android.view.ContextThemeWrapper  	resetGame  android.view.ContextThemeWrapper  run  android.view.ContextThemeWrapper  selectRandomEvent  android.view.ContextThemeWrapper  set  android.view.ContextThemeWrapper  split  android.view.ContextThemeWrapper  take  android.view.ContextThemeWrapper  testGameLoop  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  toIntOrNull  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  
trimIndent  android.view.ContextThemeWrapper  validateEventMap  android.view.ContextThemeWrapper  GONE android.view.View  OnClickListener android.view.View  VISIBLE android.view.View  postDelayed android.view.View  setOnClickListener android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  Button android.widget  LinearLayout android.widget  TextView android.widget  setOnClickListener android.widget.Button  text android.widget.Button  
visibility android.widget.Button  text android.widget.TextView  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityStartBinding #androidx.activity.ComponentActivity  Choice #androidx.activity.ComponentActivity  Double #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  Float #androidx.activity.ComponentActivity  
GameEngine #androidx.activity.ComponentActivity  GameEventUtils #androidx.activity.ComponentActivity  GameLoopTest #androidx.activity.ComponentActivity  GameManager #androidx.activity.ComponentActivity  GameUIState #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  Long #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  Pair #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  Triple #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  applyChoice #androidx.activity.ComponentActivity  
checkGameOver #androidx.activity.ComponentActivity  
component1 #androidx.activity.ComponentActivity  
component2 #androidx.activity.ComponentActivity  contains #androidx.activity.ComponentActivity  	emptyList #androidx.activity.ComponentActivity  emptyMap #androidx.activity.ComponentActivity  filter #androidx.activity.ComponentActivity  forEachIndexed #androidx.activity.ComponentActivity  getEvent #androidx.activity.ComponentActivity  getEventMapStatistics #androidx.activity.ComponentActivity  getGameOverReason #androidx.activity.ComponentActivity  initializeGame #androidx.activity.ComponentActivity  isChoiceAvailable #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  
isNullOrEmpty #androidx.activity.ComponentActivity  	isVictory #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  joinToString #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  
loadEvents #androidx.activity.ComponentActivity  mapOf #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  mutableMapOf #androidx.activity.ComponentActivity  
onBackPressed #androidx.activity.ComponentActivity  performNightPhase #androidx.activity.ComponentActivity  	resetGame #androidx.activity.ComponentActivity  run #androidx.activity.ComponentActivity  selectRandomEvent #androidx.activity.ComponentActivity  set #androidx.activity.ComponentActivity  split #androidx.activity.ComponentActivity  take #androidx.activity.ComponentActivity  testGameLoop #androidx.activity.ComponentActivity  to #androidx.activity.ComponentActivity  toIntOrNull #androidx.activity.ComponentActivity  trim #androidx.activity.ComponentActivity  
trimIndent #androidx.activity.ComponentActivity  validateEventMap #androidx.activity.ComponentActivity  	ActionBar androidx.appcompat.app  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  hide  androidx.appcompat.app.ActionBar  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityStartBinding (androidx.appcompat.app.AppCompatActivity  Choice (androidx.appcompat.app.AppCompatActivity  Double (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  Float (androidx.appcompat.app.AppCompatActivity  
GameEngine (androidx.appcompat.app.AppCompatActivity  GameEventUtils (androidx.appcompat.app.AppCompatActivity  GameLoopTest (androidx.appcompat.app.AppCompatActivity  GameManager (androidx.appcompat.app.AppCompatActivity  GameUIState (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  Long (androidx.appcompat.app.AppCompatActivity  MainActivity (androidx.appcompat.app.AppCompatActivity  Pair (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  TAG (androidx.appcompat.app.AppCompatActivity  Triple (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  applyChoice (androidx.appcompat.app.AppCompatActivity  
checkGameOver (androidx.appcompat.app.AppCompatActivity  
component1 (androidx.appcompat.app.AppCompatActivity  
component2 (androidx.appcompat.app.AppCompatActivity  contains (androidx.appcompat.app.AppCompatActivity  	emptyList (androidx.appcompat.app.AppCompatActivity  emptyMap (androidx.appcompat.app.AppCompatActivity  filter (androidx.appcompat.app.AppCompatActivity  forEachIndexed (androidx.appcompat.app.AppCompatActivity  getEvent (androidx.appcompat.app.AppCompatActivity  getEventMapStatistics (androidx.appcompat.app.AppCompatActivity  getGameOverReason (androidx.appcompat.app.AppCompatActivity  initializeGame (androidx.appcompat.app.AppCompatActivity  isChoiceAvailable (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  
isNullOrEmpty (androidx.appcompat.app.AppCompatActivity  	isVictory (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  joinToString (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  listOf (androidx.appcompat.app.AppCompatActivity  
loadEvents (androidx.appcompat.app.AppCompatActivity  mapOf (androidx.appcompat.app.AppCompatActivity  
mutableListOf (androidx.appcompat.app.AppCompatActivity  mutableMapOf (androidx.appcompat.app.AppCompatActivity  
onBackPressed (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  performNightPhase (androidx.appcompat.app.AppCompatActivity  	resetGame (androidx.appcompat.app.AppCompatActivity  run (androidx.appcompat.app.AppCompatActivity  selectRandomEvent (androidx.appcompat.app.AppCompatActivity  set (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  split (androidx.appcompat.app.AppCompatActivity  supportActionBar (androidx.appcompat.app.AppCompatActivity  take (androidx.appcompat.app.AppCompatActivity  testGameLoop (androidx.appcompat.app.AppCompatActivity  to (androidx.appcompat.app.AppCompatActivity  toIntOrNull (androidx.appcompat.app.AppCompatActivity  trim (androidx.appcompat.app.AppCompatActivity  
trimIndent (androidx.appcompat.app.AppCompatActivity  validateEventMap (androidx.appcompat.app.AppCompatActivity  isSystemInDarkTheme androidx.compose.foundation  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextUnit androidx.compose.ui.unit  sp androidx.compose.ui.unit  ConstraintLayout  androidx.constraintlayout.widget  postDelayed 1androidx.constraintlayout.widget.ConstraintLayout  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityStartBinding #androidx.core.app.ComponentActivity  Any #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Choice #androidx.core.app.ComponentActivity  
Deprecated #androidx.core.app.ComponentActivity  Double #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Float #androidx.core.app.ComponentActivity  
GameEngine #androidx.core.app.ComponentActivity  	GameEvent #androidx.core.app.ComponentActivity  GameEventUtils #androidx.core.app.ComponentActivity  GameLoopTest #androidx.core.app.ComponentActivity  GameManager #androidx.core.app.ComponentActivity  	GameState #androidx.core.app.ComponentActivity  GameUIState #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  Long #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  Map #androidx.core.app.ComponentActivity  Pair #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  Triple #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  applyChoice #androidx.core.app.ComponentActivity  
checkGameOver #androidx.core.app.ComponentActivity  
component1 #androidx.core.app.ComponentActivity  
component2 #androidx.core.app.ComponentActivity  contains #androidx.core.app.ComponentActivity  	emptyList #androidx.core.app.ComponentActivity  emptyMap #androidx.core.app.ComponentActivity  filter #androidx.core.app.ComponentActivity  forEachIndexed #androidx.core.app.ComponentActivity  getEvent #androidx.core.app.ComponentActivity  getEventMapStatistics #androidx.core.app.ComponentActivity  getGameOverReason #androidx.core.app.ComponentActivity  initializeGame #androidx.core.app.ComponentActivity  isChoiceAvailable #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  
isNullOrEmpty #androidx.core.app.ComponentActivity  	isVictory #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  joinToString #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  
loadEvents #androidx.core.app.ComponentActivity  mapOf #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  mutableMapOf #androidx.core.app.ComponentActivity  performNightPhase #androidx.core.app.ComponentActivity  	resetGame #androidx.core.app.ComponentActivity  run #androidx.core.app.ComponentActivity  selectRandomEvent #androidx.core.app.ComponentActivity  set #androidx.core.app.ComponentActivity  split #androidx.core.app.ComponentActivity  take #androidx.core.app.ComponentActivity  testGameLoop #androidx.core.app.ComponentActivity  to #androidx.core.app.ComponentActivity  toIntOrNull #androidx.core.app.ComponentActivity  trim #androidx.core.app.ComponentActivity  
trimIndent #androidx.core.app.ComponentActivity  validateEventMap #androidx.core.app.ComponentActivity  NightPhaseResult /androidx.core.app.ComponentActivity.GameManager  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityStartBinding &androidx.fragment.app.FragmentActivity  Choice &androidx.fragment.app.FragmentActivity  Double &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  Float &androidx.fragment.app.FragmentActivity  
GameEngine &androidx.fragment.app.FragmentActivity  GameEventUtils &androidx.fragment.app.FragmentActivity  GameLoopTest &androidx.fragment.app.FragmentActivity  GameManager &androidx.fragment.app.FragmentActivity  GameUIState &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  Long &androidx.fragment.app.FragmentActivity  MainActivity &androidx.fragment.app.FragmentActivity  Pair &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  TAG &androidx.fragment.app.FragmentActivity  Triple &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  applyChoice &androidx.fragment.app.FragmentActivity  
checkGameOver &androidx.fragment.app.FragmentActivity  
component1 &androidx.fragment.app.FragmentActivity  
component2 &androidx.fragment.app.FragmentActivity  contains &androidx.fragment.app.FragmentActivity  	emptyList &androidx.fragment.app.FragmentActivity  emptyMap &androidx.fragment.app.FragmentActivity  filter &androidx.fragment.app.FragmentActivity  forEachIndexed &androidx.fragment.app.FragmentActivity  getEvent &androidx.fragment.app.FragmentActivity  getEventMapStatistics &androidx.fragment.app.FragmentActivity  getGameOverReason &androidx.fragment.app.FragmentActivity  initializeGame &androidx.fragment.app.FragmentActivity  isChoiceAvailable &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  
isNullOrEmpty &androidx.fragment.app.FragmentActivity  	isVictory &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  joinToString &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  listOf &androidx.fragment.app.FragmentActivity  
loadEvents &androidx.fragment.app.FragmentActivity  mapOf &androidx.fragment.app.FragmentActivity  
mutableListOf &androidx.fragment.app.FragmentActivity  mutableMapOf &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  performNightPhase &androidx.fragment.app.FragmentActivity  	resetGame &androidx.fragment.app.FragmentActivity  run &androidx.fragment.app.FragmentActivity  selectRandomEvent &androidx.fragment.app.FragmentActivity  set &androidx.fragment.app.FragmentActivity  split &androidx.fragment.app.FragmentActivity  take &androidx.fragment.app.FragmentActivity  testGameLoop &androidx.fragment.app.FragmentActivity  to &androidx.fragment.app.FragmentActivity  toIntOrNull &androidx.fragment.app.FragmentActivity  trim &androidx.fragment.app.FragmentActivity  
trimIndent &androidx.fragment.app.FragmentActivity  validateEventMap &androidx.fragment.app.FragmentActivity  ActivityMainBinding com.ainative.mountainsurvival  ActivityStartBinding com.ainative.mountainsurvival  Any com.ainative.mountainsurvival  AppCompatActivity com.ainative.mountainsurvival  Boolean com.ainative.mountainsurvival  Bundle com.ainative.mountainsurvival  	ByteArray com.ainative.mountainsurvival  Charsets com.ainative.mountainsurvival  Choice com.ainative.mountainsurvival  ChoiceResult com.ainative.mountainsurvival  Context com.ainative.mountainsurvival  
DayTestResult com.ainative.mountainsurvival  
Deprecated com.ainative.mountainsurvival  Double com.ainative.mountainsurvival  	EatResult com.ainative.mountainsurvival  EventContainer com.ainative.mountainsurvival  EventEngine com.ainative.mountainsurvival  EventFlowTestResult com.ainative.mountainsurvival  EventTestResult com.ainative.mountainsurvival  
EventTestStep com.ainative.mountainsurvival  	Exception com.ainative.mountainsurvival  Float com.ainative.mountainsurvival  
GameEngine com.ainative.mountainsurvival  GameEngineTest com.ainative.mountainsurvival  	GameEvent com.ainative.mountainsurvival  GameEventUtils com.ainative.mountainsurvival  GameLoopTest com.ainative.mountainsurvival  GameLoopTestResult com.ainative.mountainsurvival  GameManager com.ainative.mountainsurvival  GameOverResult com.ainative.mountainsurvival  GameOverTestResult com.ainative.mountainsurvival  	GameState com.ainative.mountainsurvival  GameStateSnapshot com.ainative.mountainsurvival  GameStatistics com.ainative.mountainsurvival  GameSystemTest com.ainative.mountainsurvival  GameUIState com.ainative.mountainsurvival  Gson com.ainative.mountainsurvival  IOException com.ainative.mountainsurvival  Int com.ainative.mountainsurvival  Intent com.ainative.mountainsurvival  JsonSyntaxException com.ainative.mountainsurvival  List com.ainative.mountainsurvival  Log com.ainative.mountainsurvival  Long com.ainative.mountainsurvival  MainActivity com.ainative.mountainsurvival  Map com.ainative.mountainsurvival  Math com.ainative.mountainsurvival  
MutableSet com.ainative.mountainsurvival  NightPhaseResult com.ainative.mountainsurvival  NightSettlementResult com.ainative.mountainsurvival  Pair com.ainative.mountainsurvival  PerformanceTestResult com.ainative.mountainsurvival  RandomChoice com.ainative.mountainsurvival  
StartActivity com.ainative.mountainsurvival  String com.ainative.mountainsurvival  System com.ainative.mountainsurvival  SystemTestResult com.ainative.mountainsurvival  TAG com.ainative.mountainsurvival  
TestResult com.ainative.mountainsurvival  Triple com.ainative.mountainsurvival  View com.ainative.mountainsurvival  android com.ainative.mountainsurvival  any com.ainative.mountainsurvival  applyChoice com.ainative.mountainsurvival  average com.ainative.mountainsurvival  
checkGameOver com.ainative.mountainsurvival  
coerceAtLeast com.ainative.mountainsurvival  coerceAtMost com.ainative.mountainsurvival  coerceIn com.ainative.mountainsurvival  
component1 com.ainative.mountainsurvival  
component2 com.ainative.mountainsurvival  contains com.ainative.mountainsurvival  count com.ainative.mountainsurvival  	emptyList com.ainative.mountainsurvival  emptyMap com.ainative.mountainsurvival  filter com.ainative.mountainsurvival  filterIsInstance com.ainative.mountainsurvival  find com.ainative.mountainsurvival  forEach com.ainative.mountainsurvival  forEachIndexed com.ainative.mountainsurvival  getEvent com.ainative.mountainsurvival  getEventMapStatistics com.ainative.mountainsurvival  getGameOverReason com.ainative.mountainsurvival  getGameStatistics com.ainative.mountainsurvival  hasEvent com.ainative.mountainsurvival  initializeGame com.ainative.mountainsurvival  invoke com.ainative.mountainsurvival  isBlank com.ainative.mountainsurvival  isChoiceAvailable com.ainative.mountainsurvival  isEmpty com.ainative.mountainsurvival  
isNotBlank com.ainative.mountainsurvival  
isNotEmpty com.ainative.mountainsurvival  
isNullOrEmpty com.ainative.mountainsurvival  	isVictory com.ainative.mountainsurvival  java com.ainative.mountainsurvival  joinToString com.ainative.mountainsurvival  let com.ainative.mountainsurvival  listOf com.ainative.mountainsurvival  
loadEvents com.ainative.mountainsurvival  	lowercase com.ainative.mountainsurvival  map com.ainative.mountainsurvival  mapOf com.ainative.mountainsurvival  maxOfOrNull com.ainative.mountainsurvival  	maxOrNull com.ainative.mountainsurvival  minOfOrNull com.ainative.mountainsurvival  	minOrNull com.ainative.mountainsurvival  minusAssign com.ainative.mountainsurvival  
mutableListOf com.ainative.mountainsurvival  mutableMapOf com.ainative.mountainsurvival  mutableSetOf com.ainative.mountainsurvival  performNightPhase com.ainative.mountainsurvival  
plusAssign com.ainative.mountainsurvival  repeat com.ainative.mountainsurvival  	resetGame com.ainative.mountainsurvival  run com.ainative.mountainsurvival  selectRandomEvent com.ainative.mountainsurvival  set com.ainative.mountainsurvival  split com.ainative.mountainsurvival  sumOf com.ainative.mountainsurvival  take com.ainative.mountainsurvival  testGameLoop com.ainative.mountainsurvival  to com.ainative.mountainsurvival  toIntOrNull com.ainative.mountainsurvival  toList com.ainative.mountainsurvival  toMap com.ainative.mountainsurvival  trim com.ainative.mountainsurvival  
trimIndent com.ainative.mountainsurvival  until com.ainative.mountainsurvival  use com.ainative.mountainsurvival  
validateEvent com.ainative.mountainsurvival  validateEventMap com.ainative.mountainsurvival  effects $com.ainative.mountainsurvival.Choice  let $com.ainative.mountainsurvival.Choice  nextEventId $com.ainative.mountainsurvival.Choice  requirements $com.ainative.mountainsurvival.Choice  
resultText $com.ainative.mountainsurvival.Choice  specialItemGained $com.ainative.mountainsurvival.Choice  specialItemReward $com.ainative.mountainsurvival.Choice  specialItemUsed $com.ainative.mountainsurvival.Choice  text $com.ainative.mountainsurvival.Choice  events ,com.ainative.mountainsurvival.EventContainer  	ByteArray )com.ainative.mountainsurvival.EventEngine  Charsets )com.ainative.mountainsurvival.EventEngine  EventContainer )com.ainative.mountainsurvival.EventEngine  GameEventUtils )com.ainative.mountainsurvival.EventEngine  Gson )com.ainative.mountainsurvival.EventEngine  String )com.ainative.mountainsurvival.EventEngine  any )com.ainative.mountainsurvival.EventEngine  contains )com.ainative.mountainsurvival.EventEngine  context )com.ainative.mountainsurvival.EventEngine  count )com.ainative.mountainsurvival.EventEngine  	emptyList )com.ainative.mountainsurvival.EventEngine  eventContainer )com.ainative.mountainsurvival.EventEngine  filter )com.ainative.mountainsurvival.EventEngine  find )com.ainative.mountainsurvival.EventEngine  getAllEvents )com.ainative.mountainsurvival.EventEngine  getEvent )com.ainative.mountainsurvival.EventEngine  gson )com.ainative.mountainsurvival.EventEngine  hasEvent )com.ainative.mountainsurvival.EventEngine  invoke )com.ainative.mountainsurvival.EventEngine  isChoiceAvailable )com.ainative.mountainsurvival.EventEngine  
isNotEmpty )com.ainative.mountainsurvival.EventEngine  
isNullOrEmpty )com.ainative.mountainsurvival.EventEngine  java )com.ainative.mountainsurvival.EventEngine  let )com.ainative.mountainsurvival.EventEngine  loadJsonFromAssets )com.ainative.mountainsurvival.EventEngine  	lowercase )com.ainative.mountainsurvival.EventEngine  mapOf )com.ainative.mountainsurvival.EventEngine  
mutableListOf )com.ainative.mountainsurvival.EventEngine  selectRandomEvent )com.ainative.mountainsurvival.EventEngine  sumOf )com.ainative.mountainsurvival.EventEngine  to )com.ainative.mountainsurvival.EventEngine  
validateEvent )com.ainative.mountainsurvival.EventEngine  	ByteArray (com.ainative.mountainsurvival.GameEngine  Charsets (com.ainative.mountainsurvival.GameEngine  EVENTS_FILE_NAME (com.ainative.mountainsurvival.GameEngine  EventContainer (com.ainative.mountainsurvival.GameEngine  Gson (com.ainative.mountainsurvival.GameEngine  Log (com.ainative.mountainsurvival.GameEngine  String (com.ainative.mountainsurvival.GameEngine  TAG (com.ainative.mountainsurvival.GameEngine  convertEventsToMap (com.ainative.mountainsurvival.GameEngine  count (com.ainative.mountainsurvival.GameEngine  emptyMap (com.ainative.mountainsurvival.GameEngine  getEvent (com.ainative.mountainsurvival.GameEngine  getEventMapStatistics (com.ainative.mountainsurvival.GameEngine  gson (com.ainative.mountainsurvival.GameEngine  hasEvent (com.ainative.mountainsurvival.GameEngine  invoke (com.ainative.mountainsurvival.GameEngine  isBlank (com.ainative.mountainsurvival.GameEngine  isEmpty (com.ainative.mountainsurvival.GameEngine  
isNotBlank (com.ainative.mountainsurvival.GameEngine  
isNullOrEmpty (com.ainative.mountainsurvival.GameEngine  java (com.ainative.mountainsurvival.GameEngine  let (com.ainative.mountainsurvival.GameEngine  
loadEvents (com.ainative.mountainsurvival.GameEngine  logEventStatistics (com.ainative.mountainsurvival.GameEngine  mapOf (com.ainative.mountainsurvival.GameEngine  
mutableListOf (com.ainative.mountainsurvival.GameEngine  mutableMapOf (com.ainative.mountainsurvival.GameEngine  parseJsonToEventContainer (com.ainative.mountainsurvival.GameEngine  readJsonFromAssets (com.ainative.mountainsurvival.GameEngine  set (com.ainative.mountainsurvival.GameEngine  sumOf (com.ainative.mountainsurvival.GameEngine  to (com.ainative.mountainsurvival.GameEngine  toMap (com.ainative.mountainsurvival.GameEngine  use (com.ainative.mountainsurvival.GameEngine  validateEventMap (com.ainative.mountainsurvival.GameEngine  Any ,com.ainative.mountainsurvival.GameEngineTest  Boolean ,com.ainative.mountainsurvival.GameEngineTest  Context ,com.ainative.mountainsurvival.GameEngineTest  Double ,com.ainative.mountainsurvival.GameEngineTest  EventTestResult ,com.ainative.mountainsurvival.GameEngineTest  
GameEngine ,com.ainative.mountainsurvival.GameEngineTest  	GameEvent ,com.ainative.mountainsurvival.GameEngineTest  Int ,com.ainative.mountainsurvival.GameEngineTest  List ,com.ainative.mountainsurvival.GameEngineTest  Log ,com.ainative.mountainsurvival.GameEngineTest  Long ,com.ainative.mountainsurvival.GameEngineTest  Map ,com.ainative.mountainsurvival.GameEngineTest  Pair ,com.ainative.mountainsurvival.GameEngineTest  PerformanceTestResult ,com.ainative.mountainsurvival.GameEngineTest  String ,com.ainative.mountainsurvival.GameEngineTest  System ,com.ainative.mountainsurvival.GameEngineTest  TAG ,com.ainative.mountainsurvival.GameEngineTest  
TestResult ,com.ainative.mountainsurvival.GameEngineTest  average ,com.ainative.mountainsurvival.GameEngineTest  	emptyList ,com.ainative.mountainsurvival.GameEngineTest  emptyMap ,com.ainative.mountainsurvival.GameEngineTest  forEachIndexed ,com.ainative.mountainsurvival.GameEngineTest  getEvent ,com.ainative.mountainsurvival.GameEngineTest  getEventMapStatistics ,com.ainative.mountainsurvival.GameEngineTest  hasEvent ,com.ainative.mountainsurvival.GameEngineTest  isBlank ,com.ainative.mountainsurvival.GameEngineTest  
isNullOrEmpty ,com.ainative.mountainsurvival.GameEngineTest  let ,com.ainative.mountainsurvival.GameEngineTest  listOf ,com.ainative.mountainsurvival.GameEngineTest  
loadEvents ,com.ainative.mountainsurvival.GameEngineTest  map ,com.ainative.mountainsurvival.GameEngineTest  	maxOrNull ,com.ainative.mountainsurvival.GameEngineTest  	minOrNull ,com.ainative.mountainsurvival.GameEngineTest  
mutableListOf ,com.ainative.mountainsurvival.GameEngineTest  repeat ,com.ainative.mountainsurvival.GameEngineTest  to ,com.ainative.mountainsurvival.GameEngineTest  validateEventMap ,com.ainative.mountainsurvival.GameEngineTest  choices 'com.ainative.mountainsurvival.GameEvent  conditionalEvents 'com.ainative.mountainsurvival.GameEvent  effects 'com.ainative.mountainsurvival.GameEvent  id 'com.ainative.mountainsurvival.GameEvent  
randomChoices 'com.ainative.mountainsurvival.GameEvent  text 'com.ainative.mountainsurvival.GameEvent  Math ,com.ainative.mountainsurvival.GameEventUtils  android ,com.ainative.mountainsurvival.GameEventUtils  
component1 ,com.ainative.mountainsurvival.GameEventUtils  
component2 ,com.ainative.mountainsurvival.GameEventUtils  convertToInt ,com.ainative.mountainsurvival.GameEventUtils  	emptyList ,com.ainative.mountainsurvival.GameEventUtils  filterIsInstance ,com.ainative.mountainsurvival.GameEventUtils  isBlank ,com.ainative.mountainsurvival.GameEventUtils  isChoiceAvailable ,com.ainative.mountainsurvival.GameEventUtils  
isNullOrEmpty ,com.ainative.mountainsurvival.GameEventUtils  listOf ,com.ainative.mountainsurvival.GameEventUtils  
plusAssign ,com.ainative.mountainsurvival.GameEventUtils  selectRandomEvent ,com.ainative.mountainsurvival.GameEventUtils  toIntOrNull ,com.ainative.mountainsurvival.GameEventUtils  
validateEvent ,com.ainative.mountainsurvival.GameEventUtils  Boolean *com.ainative.mountainsurvival.GameLoopTest  Choice *com.ainative.mountainsurvival.GameLoopTest  
DayTestResult *com.ainative.mountainsurvival.GameLoopTest  	Exception *com.ainative.mountainsurvival.GameLoopTest  GameLoopTestResult *com.ainative.mountainsurvival.GameLoopTest  GameManager *com.ainative.mountainsurvival.GameLoopTest  GameOverTestResult *com.ainative.mountainsurvival.GameLoopTest  	GameState *com.ainative.mountainsurvival.GameLoopTest  Int *com.ainative.mountainsurvival.GameLoopTest  List *com.ainative.mountainsurvival.GameLoopTest  Log *com.ainative.mountainsurvival.GameLoopTest  Long *com.ainative.mountainsurvival.GameLoopTest  String *com.ainative.mountainsurvival.GameLoopTest  System *com.ainative.mountainsurvival.GameLoopTest  TAG *com.ainative.mountainsurvival.GameLoopTest  applyChoice *com.ainative.mountainsurvival.GameLoopTest  
checkGameOver *com.ainative.mountainsurvival.GameLoopTest  getGameOverReason *com.ainative.mountainsurvival.GameLoopTest  initializeGame *com.ainative.mountainsurvival.GameLoopTest  	isVictory *com.ainative.mountainsurvival.GameLoopTest  mapOf *com.ainative.mountainsurvival.GameLoopTest  
mutableListOf *com.ainative.mountainsurvival.GameLoopTest  performNightPhase *com.ainative.mountainsurvival.GameLoopTest  repeat *com.ainative.mountainsurvival.GameLoopTest  testGameLoop *com.ainative.mountainsurvival.GameLoopTest  to *com.ainative.mountainsurvival.GameLoopTest  issues =com.ainative.mountainsurvival.GameLoopTest.GameLoopTestResult  success =com.ainative.mountainsurvival.GameLoopTest.GameLoopTestResult  NightPhaseResult 6com.ainative.mountainsurvival.GameLoopTest.GameManager  Any )com.ainative.mountainsurvival.GameManager  Boolean )com.ainative.mountainsurvival.GameManager  Choice )com.ainative.mountainsurvival.GameManager  ChoiceResult )com.ainative.mountainsurvival.GameManager  Double )com.ainative.mountainsurvival.GameManager  	EatResult )com.ainative.mountainsurvival.GameManager  Float )com.ainative.mountainsurvival.GameManager  GameOverResult )com.ainative.mountainsurvival.GameManager  	GameState )com.ainative.mountainsurvival.GameManager  GameStateSnapshot )com.ainative.mountainsurvival.GameManager  GameStatistics )com.ainative.mountainsurvival.GameManager  Int )com.ainative.mountainsurvival.GameManager  List )com.ainative.mountainsurvival.GameManager  Log )com.ainative.mountainsurvival.GameManager  Long )com.ainative.mountainsurvival.GameManager  Map )com.ainative.mountainsurvival.GameManager  NightPhaseResult )com.ainative.mountainsurvival.GameManager  NightSettlementResult )com.ainative.mountainsurvival.GameManager  Pair )com.ainative.mountainsurvival.GameManager  String )com.ainative.mountainsurvival.GameManager  System )com.ainative.mountainsurvival.GameManager  TAG )com.ainative.mountainsurvival.GameManager  applyChoice )com.ainative.mountainsurvival.GameManager  calculateTotalFirewoodCollected )com.ainative.mountainsurvival.GameManager  calculateTotalFoodConsumed )com.ainative.mountainsurvival.GameManager  
checkGameOver )com.ainative.mountainsurvival.GameManager  
coerceAtLeast )com.ainative.mountainsurvival.GameManager  coerceAtMost )com.ainative.mountainsurvival.GameManager  
component1 )com.ainative.mountainsurvival.GameManager  
component2 )com.ainative.mountainsurvival.GameManager  convertToInt )com.ainative.mountainsurvival.GameManager  emptyMap )com.ainative.mountainsurvival.GameManager  gameHistory )com.ainative.mountainsurvival.GameManager  	gameState )com.ainative.mountainsurvival.GameManager  getGameOverReason )com.ainative.mountainsurvival.GameManager  getGameOverResult )com.ainative.mountainsurvival.GameManager  getGameStatistics )com.ainative.mountainsurvival.GameManager  getPropertyValue )com.ainative.mountainsurvival.GameManager  initializeGame )com.ainative.mountainsurvival.GameManager  
isInitialized )com.ainative.mountainsurvival.GameManager  	isVictory )com.ainative.mountainsurvival.GameManager  let )com.ainative.mountainsurvival.GameManager  mapOf )com.ainative.mountainsurvival.GameManager  maxOfOrNull )com.ainative.mountainsurvival.GameManager  minOfOrNull )com.ainative.mountainsurvival.GameManager  minusAssign )com.ainative.mountainsurvival.GameManager  
mutableListOf )com.ainative.mountainsurvival.GameManager  mutableMapOf )com.ainative.mountainsurvival.GameManager  mutableSetOf )com.ainative.mountainsurvival.GameManager  performNightPhase )com.ainative.mountainsurvival.GameManager  
plusAssign )com.ainative.mountainsurvival.GameManager  	resetGame )com.ainative.mountainsurvival.GameManager  saveStateSnapshot )com.ainative.mountainsurvival.GameManager  set )com.ainative.mountainsurvival.GameManager  to )com.ainative.mountainsurvival.GameManager  toIntOrNull )com.ainative.mountainsurvival.GameManager  toList )com.ainative.mountainsurvival.GameManager  until )com.ainative.mountainsurvival.GameManager  gameOverResult 6com.ainative.mountainsurvival.GameManager.ChoiceResult  message 6com.ainative.mountainsurvival.GameManager.ChoiceResult  specialItemGained 6com.ainative.mountainsurvival.GameManager.ChoiceResult  stateChanges 6com.ainative.mountainsurvival.GameManager.ChoiceResult  success 6com.ainative.mountainsurvival.GameManager.ChoiceResult  state ;com.ainative.mountainsurvival.GameManager.GameStateSnapshot  totalChoicesMade 8com.ainative.mountainsurvival.GameManager.GameStatistics  dayAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  
firewoodAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  firewoodBefore :com.ainative.mountainsurvival.GameManager.NightPhaseResult  firewoodUsed :com.ainative.mountainsurvival.GameManager.NightPhaseResult  hadEnoughFirewood :com.ainative.mountainsurvival.GameManager.NightPhaseResult  staminaAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  
staminaBefore :com.ainative.mountainsurvival.GameManager.NightPhaseResult  
staminaChange :com.ainative.mountainsurvival.GameManager.NightPhaseResult  warmthAfter :com.ainative.mountainsurvival.GameManager.NightPhaseResult  warmthBefore :com.ainative.mountainsurvival.GameManager.NightPhaseResult  warmthChange :com.ainative.mountainsurvival.GameManager.NightPhaseResult  Pair 'com.ainative.mountainsurvival.GameState  applyEffects 'com.ainative.mountainsurvival.GameState  cabinIntegrity 'com.ainative.mountainsurvival.GameState  
checkGameOver 'com.ainative.mountainsurvival.GameState  
coerceAtLeast 'com.ainative.mountainsurvival.GameState  coerceAtMost 'com.ainative.mountainsurvival.GameState  coerceIn 'com.ainative.mountainsurvival.GameState  
component1 'com.ainative.mountainsurvival.GameState  
component2 'com.ainative.mountainsurvival.GameState  copy 'com.ainative.mountainsurvival.GameState  
currentDay 'com.ainative.mountainsurvival.GameState  eat 'com.ainative.mountainsurvival.GameState  firewood 'com.ainative.mountainsurvival.GameState  food 'com.ainative.mountainsurvival.GameState  
getStatusText 'com.ainative.mountainsurvival.GameState  hope 'com.ainative.mountainsurvival.GameState  minusAssign 'com.ainative.mountainsurvival.GameState  nightTimeSettlement 'com.ainative.mountainsurvival.GameState  specialItems 'com.ainative.mountainsurvival.GameState  stamina 'com.ainative.mountainsurvival.GameState  warmth 'com.ainative.mountainsurvival.GameState  Boolean ,com.ainative.mountainsurvival.GameSystemTest  Choice ,com.ainative.mountainsurvival.GameSystemTest  Context ,com.ainative.mountainsurvival.GameSystemTest  EventFlowTestResult ,com.ainative.mountainsurvival.GameSystemTest  
EventTestStep ,com.ainative.mountainsurvival.GameSystemTest  	Exception ,com.ainative.mountainsurvival.GameSystemTest  
GameEngine ,com.ainative.mountainsurvival.GameSystemTest  GameManager ,com.ainative.mountainsurvival.GameSystemTest  	GameState ,com.ainative.mountainsurvival.GameSystemTest  Int ,com.ainative.mountainsurvival.GameSystemTest  List ,com.ainative.mountainsurvival.GameSystemTest  Log ,com.ainative.mountainsurvival.GameSystemTest  Long ,com.ainative.mountainsurvival.GameSystemTest  String ,com.ainative.mountainsurvival.GameSystemTest  System ,com.ainative.mountainsurvival.GameSystemTest  SystemTestResult ,com.ainative.mountainsurvival.GameSystemTest  TAG ,com.ainative.mountainsurvival.GameSystemTest  applyChoice ,com.ainative.mountainsurvival.GameSystemTest  getEvent ,com.ainative.mountainsurvival.GameSystemTest  getGameStatistics ,com.ainative.mountainsurvival.GameSystemTest  initializeGame ,com.ainative.mountainsurvival.GameSystemTest  
isNotEmpty ,com.ainative.mountainsurvival.GameSystemTest  
isNullOrEmpty ,com.ainative.mountainsurvival.GameSystemTest  let ,com.ainative.mountainsurvival.GameSystemTest  
loadEvents ,com.ainative.mountainsurvival.GameSystemTest  map ,com.ainative.mountainsurvival.GameSystemTest  mapOf ,com.ainative.mountainsurvival.GameSystemTest  
mutableListOf ,com.ainative.mountainsurvival.GameSystemTest  to ,com.ainative.mountainsurvival.GameSystemTest  validateEventMap ,com.ainative.mountainsurvival.GameSystemTest  GameStatistics 8com.ainative.mountainsurvival.GameSystemTest.GameManager  ActivityMainBinding *com.ainative.mountainsurvival.MainActivity  Any *com.ainative.mountainsurvival.MainActivity  Boolean *com.ainative.mountainsurvival.MainActivity  Bundle *com.ainative.mountainsurvival.MainActivity  Choice *com.ainative.mountainsurvival.MainActivity  	Companion *com.ainative.mountainsurvival.MainActivity  Double *com.ainative.mountainsurvival.MainActivity  	Exception *com.ainative.mountainsurvival.MainActivity  Float *com.ainative.mountainsurvival.MainActivity  
GameEngine *com.ainative.mountainsurvival.MainActivity  	GameEvent *com.ainative.mountainsurvival.MainActivity  GameEventUtils *com.ainative.mountainsurvival.MainActivity  GameLoopTest *com.ainative.mountainsurvival.MainActivity  GameManager *com.ainative.mountainsurvival.MainActivity  	GameState *com.ainative.mountainsurvival.MainActivity  GameUIState *com.ainative.mountainsurvival.MainActivity  Int *com.ainative.mountainsurvival.MainActivity  List *com.ainative.mountainsurvival.MainActivity  Log *com.ainative.mountainsurvival.MainActivity  Long *com.ainative.mountainsurvival.MainActivity  Map *com.ainative.mountainsurvival.MainActivity  Pair *com.ainative.mountainsurvival.MainActivity  String *com.ainative.mountainsurvival.MainActivity  TAG *com.ainative.mountainsurvival.MainActivity  Triple *com.ainative.mountainsurvival.MainActivity  View *com.ainative.mountainsurvival.MainActivity  applyChoice *com.ainative.mountainsurvival.MainActivity  binding *com.ainative.mountainsurvival.MainActivity  
checkGameOver *com.ainative.mountainsurvival.MainActivity  
component1 *com.ainative.mountainsurvival.MainActivity  
component2 *com.ainative.mountainsurvival.MainActivity  contains *com.ainative.mountainsurvival.MainActivity  convertToInt *com.ainative.mountainsurvival.MainActivity  currentChoices *com.ainative.mountainsurvival.MainActivity  currentEvent *com.ainative.mountainsurvival.MainActivity  debugEventChoices *com.ainative.mountainsurvival.MainActivity  displayEvent *com.ainative.mountainsurvival.MainActivity  	emptyList *com.ainative.mountainsurvival.MainActivity  emptyMap *com.ainative.mountainsurvival.MainActivity  evaluateCondition *com.ainative.mountainsurvival.MainActivity  evaluateConditionalEvent *com.ainative.mountainsurvival.MainActivity  eventMap *com.ainative.mountainsurvival.MainActivity  filter *com.ainative.mountainsurvival.MainActivity  finish *com.ainative.mountainsurvival.MainActivity  forEachIndexed *com.ainative.mountainsurvival.MainActivity  gameUIState *com.ainative.mountainsurvival.MainActivity  generateNightSettlementText *com.ainative.mountainsurvival.MainActivity  generateStateChangesText *com.ainative.mountainsurvival.MainActivity  getAvailableChoices *com.ainative.mountainsurvival.MainActivity  getEvent *com.ainative.mountainsurvival.MainActivity  getEventMapStatistics *com.ainative.mountainsurvival.MainActivity  getGameOverReason *com.ainative.mountainsurvival.MainActivity  handleChoiceClick *com.ainative.mountainsurvival.MainActivity  handleContinueClick *com.ainative.mountainsurvival.MainActivity  handleFoodChoice *com.ainative.mountainsurvival.MainActivity  handleFoodContinueClick *com.ainative.mountainsurvival.MainActivity  handleNightContinueClick *com.ainative.mountainsurvival.MainActivity  handleNormalChoice *com.ainative.mountainsurvival.MainActivity  initializeGame *com.ainative.mountainsurvival.MainActivity  initializeStateTransitionDP *com.ainative.mountainsurvival.MainActivity  isChoiceAvailable *com.ainative.mountainsurvival.MainActivity  
isNotEmpty *com.ainative.mountainsurvival.MainActivity  
isNullOrEmpty *com.ainative.mountainsurvival.MainActivity  	isVictory *com.ainative.mountainsurvival.MainActivity  joinToString *com.ainative.mountainsurvival.MainActivity  layoutInflater *com.ainative.mountainsurvival.MainActivity  let *com.ainative.mountainsurvival.MainActivity  listOf *com.ainative.mountainsurvival.MainActivity  
loadEvents *com.ainative.mountainsurvival.MainActivity  mapOf *com.ainative.mountainsurvival.MainActivity  
mutableListOf *com.ainative.mountainsurvival.MainActivity  mutableMapOf *com.ainative.mountainsurvival.MainActivity  
pendingChoice *com.ainative.mountainsurvival.MainActivity  pendingNightEventId *com.ainative.mountainsurvival.MainActivity  performNightPhase *com.ainative.mountainsurvival.MainActivity  performNightPhaseAndCheck *com.ainative.mountainsurvival.MainActivity  proceedToNextDay *com.ainative.mountainsurvival.MainActivity  processNextStep *com.ainative.mountainsurvival.MainActivity  	resetGame *com.ainative.mountainsurvival.MainActivity  restartGame *com.ainative.mountainsurvival.MainActivity  run *com.ainative.mountainsurvival.MainActivity  selectRandomEvent *com.ainative.mountainsurvival.MainActivity  set *com.ainative.mountainsurvival.MainActivity  setContentView *com.ainative.mountainsurvival.MainActivity  setupChoiceButtons *com.ainative.mountainsurvival.MainActivity  setupClickListeners *com.ainative.mountainsurvival.MainActivity  shouldEnterFoodPhase *com.ainative.mountainsurvival.MainActivity  showContinueButton *com.ainative.mountainsurvival.MainActivity  showFailureButtons *com.ainative.mountainsurvival.MainActivity  showFailureScreen *com.ainative.mountainsurvival.MainActivity  showFoodChoicePhase *com.ainative.mountainsurvival.MainActivity  showGameOverDialog *com.ainative.mountainsurvival.MainActivity  showNightPhaseResult *com.ainative.mountainsurvival.MainActivity  showVictoryButtons *com.ainative.mountainsurvival.MainActivity  showVictoryScreen *com.ainative.mountainsurvival.MainActivity  split *com.ainative.mountainsurvival.MainActivity  stateTransitionDP *com.ainative.mountainsurvival.MainActivity  take *com.ainative.mountainsurvival.MainActivity  testGameLoop *com.ainative.mountainsurvival.MainActivity  to *com.ainative.mountainsurvival.MainActivity  toIntOrNull *com.ainative.mountainsurvival.MainActivity  transitionState *com.ainative.mountainsurvival.MainActivity  trim *com.ainative.mountainsurvival.MainActivity  
trimIndent *com.ainative.mountainsurvival.MainActivity  updateUI *com.ainative.mountainsurvival.MainActivity  validateEventMap *com.ainative.mountainsurvival.MainActivity  ActivityMainBinding 4com.ainative.mountainsurvival.MainActivity.Companion  Choice 4com.ainative.mountainsurvival.MainActivity.Companion  
GameEngine 4com.ainative.mountainsurvival.MainActivity.Companion  GameEventUtils 4com.ainative.mountainsurvival.MainActivity.Companion  GameLoopTest 4com.ainative.mountainsurvival.MainActivity.Companion  GameManager 4com.ainative.mountainsurvival.MainActivity.Companion  GameUIState 4com.ainative.mountainsurvival.MainActivity.Companion  Log 4com.ainative.mountainsurvival.MainActivity.Companion  Pair 4com.ainative.mountainsurvival.MainActivity.Companion  TAG 4com.ainative.mountainsurvival.MainActivity.Companion  Triple 4com.ainative.mountainsurvival.MainActivity.Companion  View 4com.ainative.mountainsurvival.MainActivity.Companion  applyChoice 4com.ainative.mountainsurvival.MainActivity.Companion  
checkGameOver 4com.ainative.mountainsurvival.MainActivity.Companion  
component1 4com.ainative.mountainsurvival.MainActivity.Companion  
component2 4com.ainative.mountainsurvival.MainActivity.Companion  contains 4com.ainative.mountainsurvival.MainActivity.Companion  	emptyList 4com.ainative.mountainsurvival.MainActivity.Companion  emptyMap 4com.ainative.mountainsurvival.MainActivity.Companion  filter 4com.ainative.mountainsurvival.MainActivity.Companion  forEachIndexed 4com.ainative.mountainsurvival.MainActivity.Companion  getEvent 4com.ainative.mountainsurvival.MainActivity.Companion  getEventMapStatistics 4com.ainative.mountainsurvival.MainActivity.Companion  getGameOverReason 4com.ainative.mountainsurvival.MainActivity.Companion  initializeGame 4com.ainative.mountainsurvival.MainActivity.Companion  isChoiceAvailable 4com.ainative.mountainsurvival.MainActivity.Companion  
isNotEmpty 4com.ainative.mountainsurvival.MainActivity.Companion  
isNullOrEmpty 4com.ainative.mountainsurvival.MainActivity.Companion  	isVictory 4com.ainative.mountainsurvival.MainActivity.Companion  joinToString 4com.ainative.mountainsurvival.MainActivity.Companion  let 4com.ainative.mountainsurvival.MainActivity.Companion  listOf 4com.ainative.mountainsurvival.MainActivity.Companion  
loadEvents 4com.ainative.mountainsurvival.MainActivity.Companion  mapOf 4com.ainative.mountainsurvival.MainActivity.Companion  
mutableListOf 4com.ainative.mountainsurvival.MainActivity.Companion  mutableMapOf 4com.ainative.mountainsurvival.MainActivity.Companion  performNightPhase 4com.ainative.mountainsurvival.MainActivity.Companion  	resetGame 4com.ainative.mountainsurvival.MainActivity.Companion  run 4com.ainative.mountainsurvival.MainActivity.Companion  selectRandomEvent 4com.ainative.mountainsurvival.MainActivity.Companion  set 4com.ainative.mountainsurvival.MainActivity.Companion  split 4com.ainative.mountainsurvival.MainActivity.Companion  take 4com.ainative.mountainsurvival.MainActivity.Companion  testGameLoop 4com.ainative.mountainsurvival.MainActivity.Companion  to 4com.ainative.mountainsurvival.MainActivity.Companion  toIntOrNull 4com.ainative.mountainsurvival.MainActivity.Companion  trim 4com.ainative.mountainsurvival.MainActivity.Companion  
trimIndent 4com.ainative.mountainsurvival.MainActivity.Companion  validateEventMap 4com.ainative.mountainsurvival.MainActivity.Companion  NightPhaseResult 6com.ainative.mountainsurvival.MainActivity.GameManager  FOOD_CHOICE 6com.ainative.mountainsurvival.MainActivity.GameUIState  FOOD_WAITING_CONTINUE 6com.ainative.mountainsurvival.MainActivity.GameUIState  	GAME_OVER 6com.ainative.mountainsurvival.MainActivity.GameUIState  NIGHT_PHASE 6com.ainative.mountainsurvival.MainActivity.GameUIState  NIGHT_WAITING_CONTINUE 6com.ainative.mountainsurvival.MainActivity.GameUIState  
NORMAL_CHOICE 6com.ainative.mountainsurvival.MainActivity.GameUIState  WAITING_CONTINUE 6com.ainative.mountainsurvival.MainActivity.GameUIState  chance *com.ainative.mountainsurvival.RandomChoice  nextEventId *com.ainative.mountainsurvival.RandomChoice  ActivityStartBinding +com.ainative.mountainsurvival.StartActivity  Bundle +com.ainative.mountainsurvival.StartActivity  
Deprecated +com.ainative.mountainsurvival.StartActivity  Intent +com.ainative.mountainsurvival.StartActivity  Log +com.ainative.mountainsurvival.StartActivity  MainActivity +com.ainative.mountainsurvival.StartActivity  TAG +com.ainative.mountainsurvival.StartActivity  binding +com.ainative.mountainsurvival.StartActivity  exitGame +com.ainative.mountainsurvival.StartActivity  finish +com.ainative.mountainsurvival.StartActivity  java +com.ainative.mountainsurvival.StartActivity  layoutInflater +com.ainative.mountainsurvival.StartActivity  setContentView +com.ainative.mountainsurvival.StartActivity  setupClickListeners +com.ainative.mountainsurvival.StartActivity  
startActivity +com.ainative.mountainsurvival.StartActivity  	startGame +com.ainative.mountainsurvival.StartActivity  supportActionBar +com.ainative.mountainsurvival.StartActivity  ActivityStartBinding 5com.ainative.mountainsurvival.StartActivity.Companion  Intent 5com.ainative.mountainsurvival.StartActivity.Companion  Log 5com.ainative.mountainsurvival.StartActivity.Companion  MainActivity 5com.ainative.mountainsurvival.StartActivity.Companion  TAG 5com.ainative.mountainsurvival.StartActivity.Companion  java 5com.ainative.mountainsurvival.StartActivity.Companion  ActivityMainBinding )com.ainative.mountainsurvival.databinding  ActivityStartBinding )com.ainative.mountainsurvival.databinding  
choice1Button =com.ainative.mountainsurvival.databinding.ActivityMainBinding  
choice2Button =com.ainative.mountainsurvival.databinding.ActivityMainBinding  
choice3Button =com.ainative.mountainsurvival.databinding.ActivityMainBinding  firewoodTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  foodTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  inflate =com.ainative.mountainsurvival.databinding.ActivityMainBinding  root =com.ainative.mountainsurvival.databinding.ActivityMainBinding  staminaTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  
storyTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  warmthTextView =com.ainative.mountainsurvival.databinding.ActivityMainBinding  exitGameButton >com.ainative.mountainsurvival.databinding.ActivityStartBinding  inflate >com.ainative.mountainsurvival.databinding.ActivityStartBinding  root >com.ainative.mountainsurvival.databinding.ActivityStartBinding  startGameButton >com.ainative.mountainsurvival.databinding.ActivityStartBinding  Boolean &com.ainative.mountainsurvival.ui.theme  Build &com.ainative.mountainsurvival.ui.theme  
Composable &com.ainative.mountainsurvival.ui.theme  DarkColorScheme &com.ainative.mountainsurvival.ui.theme  
FontFamily &com.ainative.mountainsurvival.ui.theme  
FontWeight &com.ainative.mountainsurvival.ui.theme  LightColorScheme &com.ainative.mountainsurvival.ui.theme  MountainSurvivalTheme &com.ainative.mountainsurvival.ui.theme  Pink40 &com.ainative.mountainsurvival.ui.theme  Pink80 &com.ainative.mountainsurvival.ui.theme  Purple40 &com.ainative.mountainsurvival.ui.theme  Purple80 &com.ainative.mountainsurvival.ui.theme  PurpleGrey40 &com.ainative.mountainsurvival.ui.theme  PurpleGrey80 &com.ainative.mountainsurvival.ui.theme  
Typography &com.ainative.mountainsurvival.ui.theme  Unit &com.ainative.mountainsurvival.ui.theme  Gson com.google.gson  JsonSyntaxException com.google.gson  fromJson com.google.gson.Gson  	TypeToken com.google.gson.reflect  IOException java.io  InputStream java.io  printStackTrace java.io.IOException  	available java.io.InputStream  close java.io.InputStream  read java.io.InputStream  use java.io.InputStream  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  random java.lang.Math  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  Charset java.nio.charset  	ByteArray kotlin  CharSequence kotlin  
Deprecated kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  Pair kotlin  Result kotlin  String kotlin  Triple kotlin  let kotlin  map kotlin  repeat kotlin  run kotlin  to kotlin  toList kotlin  use kotlin  not kotlin.Boolean  isEmpty kotlin.CharSequence  	compareTo 
kotlin.Double  div 
kotlin.Double  plus 
kotlin.Double  
plusAssign 
kotlin.Double  sp 
kotlin.Double  toInt 
kotlin.Double  toInt kotlin.Float  
coerceAtLeast 
kotlin.Int  coerceAtMost 
kotlin.Int  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  minusAssign 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  rangeTo 
kotlin.Int  toDouble 
kotlin.Int  
unaryMinus 
kotlin.Int  minus kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.String  contains 
kotlin.String  invoke 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  split 
kotlin.String  take 
kotlin.String  to 
kotlin.String  toIntOrNull 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  invoke kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  
Collection kotlin.collections  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  any kotlin.collections  average kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  filter kotlin.collections  filterIsInstance kotlin.collections  find kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  maxOfOrNull kotlin.collections  	maxOrNull kotlin.collections  minOfOrNull kotlin.collections  	minOrNull kotlin.collections  minusAssign kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  sumOf kotlin.collections  sumOfInt kotlin.collections  take kotlin.collections  toList kotlin.collections  toMap kotlin.collections  count kotlin.collections.Collection  sumOf kotlin.collections.Collection  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  any kotlin.collections.List  count kotlin.collections.List  filter kotlin.collections.List  filterIsInstance kotlin.collections.List  find kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  
isNullOrEmpty kotlin.collections.List  iterator kotlin.collections.List  let kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  sumOf kotlin.collections.List  Entry kotlin.collections.Map  containsKey kotlin.collections.Map  get kotlin.collections.Map  isEmpty kotlin.collections.Map  
isNotEmpty kotlin.collections.Map  
isNullOrEmpty kotlin.collections.Map  let kotlin.collections.Map  size kotlin.collections.Map  values kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  average kotlin.collections.MutableList  clear kotlin.collections.MutableList  get kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  joinToString kotlin.collections.MutableList  maxOfOrNull kotlin.collections.MutableList  	maxOrNull kotlin.collections.MutableList  minOfOrNull kotlin.collections.MutableList  	minOrNull kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  size kotlin.collections.MutableList  toList kotlin.collections.MutableList  containsKey kotlin.collections.MutableMap  get kotlin.collections.MutableMap  set kotlin.collections.MutableMap  size kotlin.collections.MutableMap  toMap kotlin.collections.MutableMap  add kotlin.collections.MutableSet  contains kotlin.collections.MutableSet  remove kotlin.collections.MutableSet  size kotlin.collections.MutableSet  use 	kotlin.io  java 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  coerceIn 
kotlin.ranges  contains 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  java kotlin.reflect.KClass  Sequence kotlin.sequences  any kotlin.sequences  average kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  filter kotlin.sequences  filterIsInstance kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  maxOfOrNull kotlin.sequences  	maxOrNull kotlin.sequences  minOfOrNull kotlin.sequences  	minOrNull kotlin.sequences  sumOf kotlin.sequences  take kotlin.sequences  toList kotlin.sequences  Charsets kotlin.text  String kotlin.text  any kotlin.text  contains kotlin.text  count kotlin.text  filter kotlin.text  find kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  	lowercase kotlin.text  map kotlin.text  maxOfOrNull kotlin.text  	maxOrNull kotlin.text  minOfOrNull kotlin.text  	minOrNull kotlin.text  repeat kotlin.text  set kotlin.text  split kotlin.text  sumOf kotlin.text  take kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  trim kotlin.text  
trimIndent kotlin.text  UTF_8 kotlin.text.Charsets                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                