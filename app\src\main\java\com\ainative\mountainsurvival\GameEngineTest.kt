package com.ainative.mountainsurvival

import android.content.Context
import android.util.Log

/**
 * GameEngine 测试工具类
 * 用于验证 GameEngine 的功能和性能
 */
object GameEngineTest {
    
    private const val TAG = "GameEngineTest"
    
    /**
     * 运行完整的 GameEngine 测试
     * @param context Android上下文
     * @return 测试结果报告
     */
    fun runFullTest(context: Context): TestResult {
        Log.d(TAG, "开始 GameEngine 完整测试...")
        
        val startTime = System.currentTimeMillis()
        
        // 1. 测试事件加载
        val eventMap = GameEngine.loadEvents(context)
        val loadTime = System.currentTimeMillis() - startTime
        
        if (eventMap.isEmpty()) {
            return TestResult(
                success = false,
                message = "事件加载失败",
                loadTimeMs = loadTime
            )
        }
        
        Log.d(TAG, "事件加载成功，耗时: ${loadTime}ms")
        
        // 2. 测试事件验证
        val validationIssues = GameEngine.validateEventMap(eventMap)
        
        // 3. 测试统计信息
        val statistics = GameEngine.getEventMapStatistics(eventMap)
        
        // 4. 测试事件查找
        val testEventIds = listOf("day1_start", "day1_evening", "day2_start", "found_cans")
        val foundEvents = testEventIds.map { id ->
            id to GameEngine.hasEvent(eventMap, id)
        }
        
        // 5. 测试事件获取
        val day1Event = GameEngine.getEvent(eventMap, "day1_start")
        
        val totalTime = System.currentTimeMillis() - startTime
        
        Log.d(TAG, "GameEngine 测试完成，总耗时: ${totalTime}ms")
        
        return TestResult(
            success = true,
            message = "所有测试通过",
            loadTimeMs = loadTime,
            totalTimeMs = totalTime,
            eventCount = eventMap.size,
            validationIssues = validationIssues,
            statistics = statistics,
            foundEvents = foundEvents,
            sampleEvent = day1Event
        )
    }
    
    /**
     * 测试特定事件的完整性
     * @param context Android上下文
     * @param eventId 要测试的事件ID
     * @return 事件测试结果
     */
    fun testSpecificEvent(context: Context, eventId: String): EventTestResult {
        val eventMap = GameEngine.loadEvents(context)
        val event = GameEngine.getEvent(eventMap, eventId)
        
        if (event == null) {
            return EventTestResult(
                eventId = eventId,
                exists = false,
                message = "事件不存在"
            )
        }
        
        val issues = mutableListOf<String>()
        
        // 检查基本字段
        if (event.text.isBlank()) {
            issues.add("事件文本为空")
        }
        
        if (event.choices.isEmpty() && event.randomChoices.isNullOrEmpty()) {
            issues.add("没有任何选择")
        }
        
        // 检查选择的有效性
        event.choices.forEachIndexed { index, choice ->
            if (choice.text.isBlank()) {
                issues.add("选择 $index 的文本为空")
            }
            
            choice.nextEventId?.let { nextId ->
                if (!GameEngine.hasEvent(eventMap, nextId)) {
                    issues.add("选择 $index 引用了不存在的事件: $nextId")
                }
            }
        }
        
        return EventTestResult(
            eventId = eventId,
            exists = true,
            event = event,
            issues = issues,
            message = if (issues.isEmpty()) "事件验证通过" else "发现 ${issues.size} 个问题"
        )
    }
    
    /**
     * 性能测试
     * @param context Android上下文
     * @param iterations 测试迭代次数
     * @return 性能测试结果
     */
    fun performanceTest(context: Context, iterations: Int = 10): PerformanceTestResult {
        Log.d(TAG, "开始性能测试，迭代次数: $iterations")
        
        val loadTimes = mutableListOf<Long>()
        var totalEvents = 0
        
        repeat(iterations) { i ->
            val startTime = System.currentTimeMillis()
            val eventMap = GameEngine.loadEvents(context)
            val loadTime = System.currentTimeMillis() - startTime
            
            loadTimes.add(loadTime)
            totalEvents = eventMap.size
            
            Log.d(TAG, "迭代 ${i + 1}: 加载时间 ${loadTime}ms, 事件数量 $totalEvents")
        }
        
        val averageLoadTime = loadTimes.average()
        val minLoadTime = loadTimes.minOrNull() ?: 0L
        val maxLoadTime = loadTimes.maxOrNull() ?: 0L
        
        return PerformanceTestResult(
            iterations = iterations,
            averageLoadTimeMs = averageLoadTime,
            minLoadTimeMs = minLoadTime,
            maxLoadTimeMs = maxLoadTime,
            eventCount = totalEvents,
            loadTimes = loadTimes
        )
    }
    
    /**
     * 测试结果数据类
     */
    data class TestResult(
        val success: Boolean,
        val message: String,
        val loadTimeMs: Long,
        val totalTimeMs: Long = 0L,
        val eventCount: Int = 0,
        val validationIssues: List<String> = emptyList(),
        val statistics: Map<String, Any> = emptyMap(),
        val foundEvents: List<Pair<String, Boolean>> = emptyList(),
        val sampleEvent: GameEvent? = null
    )
    
    /**
     * 事件测试结果数据类
     */
    data class EventTestResult(
        val eventId: String,
        val exists: Boolean,
        val event: GameEvent? = null,
        val issues: List<String> = emptyList(),
        val message: String
    )
    
    /**
     * 性能测试结果数据类
     */
    data class PerformanceTestResult(
        val iterations: Int,
        val averageLoadTimeMs: Double,
        val minLoadTimeMs: Long,
        val maxLoadTimeMs: Long,
        val eventCount: Int,
        val loadTimes: List<Long>
    )
}
