package com.ainative.mountainsurvival

import android.content.Context
import android.util.Log

/**
 * 游戏系统集成测试
 * 验证所有模块是否正确连接和工作
 */
object GameSystemTest {
    
    private const val TAG = "GameSystemTest"
    
    /**
     * 运行完整的系统测试
     * @param context Android上下文
     * @return 测试结果
     */
    fun runFullSystemTest(context: Context): SystemTestResult {
        Log.d(TAG, "开始游戏系统集成测试...")
        
        val startTime = System.currentTimeMillis()
        val issues = mutableListOf<String>()
        
        try {
            // 1. 测试 GameManager 初始化
            Log.d(TAG, "测试 GameManager 初始化...")
            GameManager.initializeGame()
            val gameState = GameManager.gameState
            
            if (gameState.warmth != 80 || gameState.stamina != 100) {
                issues.add("GameManager 初始化失败：初始值不正确")
            }
            
            // 2. 测试 GameEngine 加载
            Log.d(TAG, "测试 GameEngine 事件加载...")
            val eventMap = GameEngine.loadEvents(context)
            
            if (eventMap.isEmpty()) {
                issues.add("GameEngine 加载失败：事件映射为空")
            }
            
            if (!eventMap.containsKey("day1_start")) {
                issues.add("GameEngine 加载失败：缺少 day1_start 事件")
            }
            
            // 3. 测试事件验证
            Log.d(TAG, "测试事件数据验证...")
            val validationIssues = GameEngine.validateEventMap(eventMap)
            if (validationIssues.isNotEmpty()) {
                issues.addAll(validationIssues.map { "事件验证问题: $it" })
            }
            
            // 4. 测试选择应用
            Log.d(TAG, "测试选择应用...")
            val testChoice = Choice(
                text = "测试选择",
                effects = mapOf("warmth" to -10, "stamina" to -5)
            )
            
            val beforeWarmth = gameState.warmth
            val beforeStamina = gameState.stamina
            
            val choiceResult = GameManager.applyChoice(testChoice)
            
            if (!choiceResult.success) {
                issues.add("选择应用失败: ${choiceResult.message}")
            }
            
            if (gameState.warmth != beforeWarmth - 10 || gameState.stamina != beforeStamina - 5) {
                issues.add("选择效果应用不正确")
            }
            
            // 5. 测试游戏结束检测
            Log.d(TAG, "测试游戏结束检测...")
            
            // 将体温设为0来测试游戏结束
            val gameOverChoice = Choice(
                text = "游戏结束测试",
                effects = mapOf("warmth" to -gameState.warmth)
            )
            
            val gameOverResult = GameManager.applyChoice(gameOverChoice)
            
            if (gameOverResult.gameOverResult == null) {
                issues.add("游戏结束检测失败")
            }
            
            // 6. 测试统计功能
            Log.d(TAG, "测试统计功能...")
            val statistics = GameManager.getGameStatistics()
            
            if (statistics.totalChoicesMade < 2) {
                issues.add("统计功能异常：选择次数计算错误")
            }
            
            val totalTime = System.currentTimeMillis() - startTime
            
            Log.d(TAG, "游戏系统集成测试完成，耗时: ${totalTime}ms")
            
            return SystemTestResult(
                success = issues.isEmpty(),
                totalTimeMs = totalTime,
                issues = issues,
                eventCount = eventMap.size,
                finalGameState = GameManager.gameState.copy(),
                statistics = statistics
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "系统测试过程中发生异常", e)
            issues.add("系统测试异常: ${e.message}")
            
            return SystemTestResult(
                success = false,
                totalTimeMs = System.currentTimeMillis() - startTime,
                issues = issues,
                exception = e
            )
        }
    }
    
    /**
     * 测试特定事件流程
     * @param context Android上下文
     * @param eventIds 要测试的事件ID列表
     * @return 事件流程测试结果
     */
    fun testEventFlow(context: Context, eventIds: List<String>): EventFlowTestResult {
        Log.d(TAG, "测试事件流程: $eventIds")
        
        val startTime = System.currentTimeMillis()
        val issues = mutableListOf<String>()
        val eventResults = mutableListOf<EventTestStep>()
        
        try {
            // 初始化系统
            GameManager.initializeGame()
            val eventMap = GameEngine.loadEvents(context)
            
            eventIds.forEach { eventId ->
                val event = GameEngine.getEvent(eventMap, eventId)
                
                if (event == null) {
                    issues.add("事件不存在: $eventId")
                    return@forEach
                }
                
                val beforeState = GameManager.gameState.copy()
                
                // 应用事件直接效果
                event.effects?.let { effects ->
                    val tempChoice = Choice(text = "事件效果", effects = effects)
                    GameManager.applyChoice(tempChoice)
                }
                
                val afterState = GameManager.gameState.copy()
                
                eventResults.add(
                    EventTestStep(
                        eventId = eventId,
                        eventText = event.text,
                        choiceCount = event.choices.size,
                        beforeState = beforeState,
                        afterState = afterState,
                        hasRandomChoices = !event.randomChoices.isNullOrEmpty()
                    )
                )
            }
            
            val totalTime = System.currentTimeMillis() - startTime
            
            return EventFlowTestResult(
                success = issues.isEmpty(),
                totalTimeMs = totalTime,
                issues = issues,
                eventResults = eventResults
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "事件流程测试异常", e)
            issues.add("事件流程测试异常: ${e.message}")
            
            return EventFlowTestResult(
                success = false,
                totalTimeMs = System.currentTimeMillis() - startTime,
                issues = issues,
                eventResults = eventResults,
                exception = e
            )
        }
    }
    
    /**
     * 系统测试结果数据类
     */
    data class SystemTestResult(
        val success: Boolean,
        val totalTimeMs: Long,
        val issues: List<String>,
        val eventCount: Int = 0,
        val finalGameState: GameState? = null,
        val statistics: GameManager.GameStatistics? = null,
        val exception: Exception? = null
    )
    
    /**
     * 事件流程测试结果数据类
     */
    data class EventFlowTestResult(
        val success: Boolean,
        val totalTimeMs: Long,
        val issues: List<String>,
        val eventResults: List<EventTestStep>,
        val exception: Exception? = null
    )
    
    /**
     * 事件测试步骤数据类
     */
    data class EventTestStep(
        val eventId: String,
        val eventText: String,
        val choiceCount: Int,
        val beforeState: GameState,
        val afterState: GameState,
        val hasRandomChoices: Boolean
    )
}
