package com.ainative.mountainsurvival

/**
 * 图标文本辅助类
 * 用于将emoji表情替换为纯文本描述，避免版权和兼容性问题
 */
object IconTextHelper {
    
    /**
     * 替换文本中的emoji为文字描述
     * @param text 包含emoji的原始文本
     * @return 替换后的文本
     */
    fun replaceEmojisWithText(text: String): String {
        return text
            // 火焰相关
            .replace("🔥", "[火焰]")
            .replace("🌡️", "[温度]")
            
            // 体力相关
            .replace("💪", "[体力]")
            .replace("💤", "[睡眠]")
            
            // 资源相关
            .replace("🪵", "[木柴]")
            .replace("🍖", "[食物]")
            
            // 天气相关
            .replace("❄️", "[雪花]")
            .replace("🌙", "[月亮]")
            
            // 庆祝相关
            .replace("🎉", "[庆祝]")
            .replace("🎊", "[彩带]")
            .replace("⭐", "[星星]")
            .replace("🏆", "[奖杯]")
            .replace("✅", "[完成]")
            
            // 建筑相关
            .replace("🏠", "[房屋]")
            .replace("🏔️", "[雪山]")
            
            // 情感相关
            .replace("❤️", "[爱心]")
            .replace("💔", "[破碎的心]")
            .replace("🎁", "[礼物]")
            
            // 死亡相关
            .replace("💀", "[骷髅]")
            .replace("⚰️", "[棺材]")
            
            // 时间相关
            .replace("📅", "[日历]")
            .replace("⏰", "[时钟]")
            
            // 交通工具
            .replace("🚁", "[直升机]")
            .replace("🎮", "[游戏手柄]")
            .replace("🚪", "[门]")
            .replace("🔄", "[刷新]")
    }
    
    /**
     * 获取胜利页面文本（无emoji版本）
     */
    fun getVictoryText(gameState: GameState, signalGunText: String): String {
        return """
            [庆祝][庆祝][庆祝] 恭喜！你成功存活了！ [庆祝][庆祝][庆祝]

            [星星] 雪山求生 - 胜利结局 [星星]

            经过7天的艰苦求生，你终于迎来了胜利的曙光！$signalGunText

            第8天清晨，风雪停了。一缕久违的阳光照在你的脸上，温暖如春。远处传来了救援直升机的轰鸣声，螺旋桨的声音在山谷中回荡。

            你挥舞着双手，大声呼喊。直升机发现了你，缓缓降落在雪地上。救援人员跳下飞机，向你跑来...

            你活下来了！

            [奖杯] === 生存成就 === [奖杯]
            [完成] 存活天数: 7天
            [火焰] 最终体温: ${gameState.warmth}
            [体力] 最终体力: ${gameState.stamina}
            [木柴] 剩余木柴: ${gameState.firewood}
            [食物] 剩余食物: ${gameState.food}
            [房屋] 小屋完整度: ${gameState.cabinIntegrity}%
            [爱心] 希望值: ${gameState.hope}
            [礼物] 特殊物品: ${gameState.specialItems.size}个

            你是真正的雪山求生专家！
        """.trimIndent()
    }
    
    /**
     * 获取失败页面文本（体温过低）
     */
    fun getFailureTextCold(gameState: GameState): String {
        return """
            [骷髅][骷髅][骷髅] 游戏结束 [骷髅][骷髅][骷髅]

            [雪花] 雪山求生 - 冻死结局 [雪花]

            很遗憾，你没能在严寒中坚持下去...

            你的意识渐渐模糊，壁炉的火焰在你眼中变成了遥远的星辰。寒冷如潮水般涌来，吞噬了你最后的温暖。

            你的身体渐渐失去知觉，呼吸变得越来越微弱。在生命的最后时刻，你想起了家人的温暖怀抱...

            你再也没有醒来。

            [棺材] === 死亡统计 === [棺材]
            [骷髅] 死亡原因: 体温过低
            [日历] 存活天数: ${gameState.currentDay}天
            [温度] 最终体温: ${gameState.warmth}
            [体力] 最终体力: ${gameState.stamina}
            [木柴] 剩余木柴: ${gameState.firewood}
            [食物] 剩余食物: ${gameState.food}
            [房屋] 小屋完整度: ${gameState.cabinIntegrity}%
            [破碎的心] 希望值: ${gameState.hope}

            雪山吞噬了又一个生命...
        """.trimIndent()
    }
    
    /**
     * 获取失败页面文本（体力耗尽）
     */
    fun getFailureTextExhaustion(gameState: GameState): String {
        return """
            [骷髅][骷髅][骷髅] 游戏结束 [骷髅][骷髅][骷髅]

            [体力] 雪山求生 - 力竭结局 [体力]

            很遗憾，你的身体已经无法承受更多的折磨...

            连日的求生让你精疲力竭，每一次呼吸都变得困难。你的双腿再也无法支撑你的身体，颤抖的双手再也无法握紧任何东西。

            你缓缓倒在雪地上，望着灰暗的天空。体力的完全耗尽让你无法再继续战斗下去。

            你缓缓闭上了眼睛，永远地沉睡在这片雪山中。

            [棺材] === 死亡统计 === [棺材]
            [骷髅] 死亡原因: 体力耗尽
            [日历] 存活天数: ${gameState.currentDay}天
            [温度] 最终体温: ${gameState.warmth}
            [体力] 最终体力: ${gameState.stamina}
            [木柴] 剩余木柴: ${gameState.firewood}
            [食物] 剩余食物: ${gameState.food}
            [房屋] 小屋完整度: ${gameState.cabinIntegrity}%
            [破碎的心] 希望值: ${gameState.hope}

            雪山又夺走了一个勇敢的灵魂...
        """.trimIndent()
    }
    
    /**
     * 获取通用失败页面文本
     */
    fun getFailureTextGeneric(gameState: GameState, reason: String): String {
        return """
            [骷髅][骷髅][骷髅] 游戏结束 [骷髅][骷髅][骷髅]

            [棺材] 雪山求生 - 死亡结局 [棺材]

            $reason

            [棺材] === 死亡统计 === [棺材]
            [日历] 存活天数: ${gameState.currentDay}天
            [温度] 最终体温: ${gameState.warmth}
            [体力] 最终体力: ${gameState.stamina}
            [木柴] 剩余木柴: ${gameState.firewood}
            [食物] 剩余食物: ${gameState.food}
            [房屋] 小屋完整度: ${gameState.cabinIntegrity}%
            [破碎的心] 希望值: ${gameState.hope}

            愿逝者安息...
        """.trimIndent()
    }
    
    /**
     * 获取夜晚结算文本
     */
    fun getNightSettlementText(nightResult: GameManager.NightPhaseResult): String {
        val firewoodText = if (nightResult.hadEnoughFirewood) {
            "[火焰] 壁炉中的火焰温暖地燃烧着，消耗了${nightResult.firewoodUsed}根木柴。"
        } else {
            "[雪花] 没有足够的木柴生火，寒冷侵袭着你的身体。"
        }

        val warmthText = if (nightResult.warmthChange > 0) {
            "你感到体温回升了${nightResult.warmthChange}度。"
        } else {
            "你的体温下降了${-nightResult.warmthChange}度。"
        }

        val staminaText = "[睡眠] 经过一夜的煎熬，你的体力下降了${-nightResult.staminaChange}点。"

        return "$firewoodText $warmthText $staminaText"
    }
    
    /**
     * 获取胜利按钮文本
     */
    fun getVictoryButtonTexts(): Pair<String, String> {
        return Pair("[彩带] 再次挑战", "[直升机] 离开雪山")
    }
    
    /**
     * 获取失败按钮文本
     */
    fun getFailureButtonTexts(): Pair<String, String> {
        return Pair("[刷新] 重新挑战", "[骷髅] 离开雪山")
    }
}
