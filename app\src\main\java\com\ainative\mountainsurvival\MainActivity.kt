package com.ainative.mountainsurvival

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.ainative.mountainsurvival.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private var eventMap: Map<String, GameEvent> = emptyMap()
    private var currentEvent: GameEvent? = null
    private var currentChoices: List<Choice> = emptyList()

    // 新增：游戏状态管理
    private var gameUIState: GameUIState = GameUIState.NORMAL_CHOICE
    private var pendingChoice: Choice? = null
    private var pendingNightEventId: String? = null

    // 动态规划状态转换表
    private val stateTransitionDP = mutableMapOf<Pair<GameUIState, String>, GameUIState>()

    companion object {
        private const val TAG = "MainActivity"
    }

    /**
     * 游戏UI状态枚举
     * 使用动态规划方法管理状态转换
     */
    enum class GameUIState {
        NORMAL_CHOICE,          // 正常选择状态
        WAITING_CONTINUE,       // 等待继续状态（选择结果后）
        FOOD_CHOICE,            // 夜晚前食物选择状态
        FOOD_WAITING_CONTINUE,  // 食物选择后等待继续状态
        NIGHT_PHASE,            // 夜晚阶段
        NIGHT_WAITING_CONTINUE, // 夜晚等待继续状态
        GAME_OVER              // 游戏结束
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置 View Binding
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 初始化动态规划状态转换表
        initializeStateTransitionDP()

        // 初始化 GameManager
        GameManager.initializeGame()
        Log.d(TAG, "GameManager 初始化完成")

        // 初始化 GameEngine 并加载事件数据
        eventMap = GameEngine.loadEvents(this)

        if (eventMap.isNotEmpty()) {
            // 验证事件数据
            val issues = GameEngine.validateEventMap(eventMap)
            if (issues.isNotEmpty()) {
                Log.w(TAG, "发现事件验证问题:")
                issues.forEach { issue ->
                    Log.w(TAG, "- $issue")
                }
            } else {
                Log.d(TAG, "事件数据验证通过")
            }

            // 显示统计信息
            val stats = GameEngine.getEventMapStatistics(eventMap)
            Log.d(TAG, "事件统计: $stats")

            // 运行游戏循环测试（可选，用于调试）
            val gameLoopTestResult = GameLoopTest.testGameLoop()
            Log.d(TAG, "游戏循环测试结果: 成功=${gameLoopTestResult.success}")
            if (gameLoopTestResult.issues.isNotEmpty()) {
                gameLoopTestResult.issues.forEach { issue ->
                    Log.w(TAG, "游戏循环问题: $issue")
                }
            }

            // 重新初始化游戏（因为测试可能改变了状态）
            GameManager.initializeGame()

            // 设置按钮点击监听器
            setupClickListeners()

            // 开始游戏 - 显示第一个事件
            displayEvent("day1_start")
        } else {
            // 事件加载失败的处理
            Log.e(TAG, "事件加载失败")
            binding.storyTextView.text = "游戏数据加载失败，请重新启动应用。"
        }
    }

    /**
     * 初始化动态规划状态转换表
     * 使用动态规划方法预计算所有可能的状态转换
     */
    private fun initializeStateTransitionDP() {
        Log.d(TAG, "初始化状态转换动态规划表...")

        // 定义状态转换规则
        val transitions = listOf(
            // 从正常选择状态的转换
            Triple(GameUIState.NORMAL_CHOICE, "choice_made", GameUIState.WAITING_CONTINUE),
            Triple(GameUIState.NORMAL_CHOICE, "game_over", GameUIState.GAME_OVER),

            // 从等待继续状态的转换
            Triple(GameUIState.WAITING_CONTINUE, "continue_clicked", GameUIState.NORMAL_CHOICE),
            Triple(GameUIState.WAITING_CONTINUE, "food_phase", GameUIState.FOOD_CHOICE),
            Triple(GameUIState.WAITING_CONTINUE, "night_phase", GameUIState.NIGHT_PHASE),
            Triple(GameUIState.WAITING_CONTINUE, "game_over", GameUIState.GAME_OVER),

            // 从食物选择状态的转换
            Triple(GameUIState.FOOD_CHOICE, "food_choice_made", GameUIState.FOOD_WAITING_CONTINUE),
            Triple(GameUIState.FOOD_CHOICE, "night_phase", GameUIState.NIGHT_PHASE),
            Triple(GameUIState.FOOD_CHOICE, "game_over", GameUIState.GAME_OVER),

            // 从食物等待继续状态的转换
            Triple(GameUIState.FOOD_WAITING_CONTINUE, "food_continue_clicked", GameUIState.FOOD_CHOICE),
            Triple(GameUIState.FOOD_WAITING_CONTINUE, "night_phase", GameUIState.NIGHT_PHASE),
            Triple(GameUIState.FOOD_WAITING_CONTINUE, "game_over", GameUIState.GAME_OVER),

            // 从夜晚阶段的转换
            Triple(GameUIState.NIGHT_PHASE, "night_shown", GameUIState.NIGHT_WAITING_CONTINUE),
            Triple(GameUIState.NIGHT_PHASE, "game_over", GameUIState.GAME_OVER),

            // 从夜晚等待继续状态的转换
            Triple(GameUIState.NIGHT_WAITING_CONTINUE, "night_continue_clicked", GameUIState.NORMAL_CHOICE),
            Triple(GameUIState.NIGHT_WAITING_CONTINUE, "game_over", GameUIState.GAME_OVER),

            // 游戏结束状态（终态）
            Triple(GameUIState.GAME_OVER, "restart", GameUIState.NORMAL_CHOICE)
        )

        // 使用动态规划填充状态转换表
        transitions.forEach { (fromState, action, toState) ->
            stateTransitionDP[Pair(fromState, action)] = toState
        }

        Log.d(TAG, "状态转换表初始化完成，共 ${stateTransitionDP.size} 个转换规则")
    }

    /**
     * 使用动态规划进行状态转换
     * @param action 触发的动作
     * @return 是否成功转换状态
     */
    private fun transitionState(action: String): Boolean {
        val newState = stateTransitionDP[Pair(gameUIState, action)]

        return if (newState != null) {
            val oldState = gameUIState
            gameUIState = newState
            Log.d(TAG, "状态转换: $oldState -> $newState (动作: $action)")
            true
        } else {
            Log.w(TAG, "无效的状态转换: $gameUIState + $action")
            false
        }
    }

    /**
     * 更新UI状态显示
     * 专门负责读取 GameManager.gameState 的当前值，并将它们更新到顶部的四个 TextView 上
     */
    private fun updateUI() {
        val gameState = GameManager.gameState

        // 更新状态显示
        binding.warmthTextView.text = gameState.getStatusText("warmth")
        binding.staminaTextView.text = gameState.getStatusText("stamina")
        binding.firewoodTextView.text = gameState.getStatusText("firewood")
        binding.foodTextView.text = gameState.getStatusText("food")

        Log.d(TAG, "UI状态更新: 体温=${gameState.warmth}, 体力=${gameState.stamina}, 木柴=${gameState.firewood}, 食物=${gameState.food}")
    }

    /**
     * 显示事件
     * 根据ID从 GameEngine 获取事件，然后将事件文本和选项文本设置到对应的 TextView 和 Button 上
     *
     * @param eventId 事件ID
     */
    private fun displayEvent(eventId: String) {
        Log.d(TAG, "显示事件: $eventId，当前UI状态: $gameUIState")

        // 确保状态为正常选择状态（强制重置，确保显示新事件时状态正确）
        if (gameUIState != GameUIState.NORMAL_CHOICE) {
            gameUIState = GameUIState.NORMAL_CHOICE
            Log.d(TAG, "强制重置UI状态为正常选择，准备显示新事件")
        }

        // 从 GameEngine 获取事件
        currentEvent = GameEngine.getEvent(eventMap, eventId)

        if (currentEvent == null) {
            Log.e(TAG, "事件不存在: $eventId")
            binding.storyTextView.text = "错误：找不到事件 $eventId"
            return
        }

        val event = currentEvent!!

        // 特殊处理：如果是夜晚事件，执行夜晚结算
        if (eventId.contains("night")) {
            Log.d(TAG, "检测到夜晚事件，执行夜晚结算")
            val nightResult = GameManager.performNightPhase()

            // 生成夜晚结算文本
            val nightSettlementText = generateNightSettlementText(nightResult)

            // 组合事件文本和结算文本
            val combinedText = "${event.text}\n\n$nightSettlementText"
            binding.storyTextView.text = combinedText

            // 更新UI显示
            updateUI()

            // 检查游戏是否结束（夜晚结算后可能导致死亡）
            if (GameManager.checkGameOver()) {
                Log.d(TAG, "夜晚结算后游戏结束")
                // 延迟显示游戏结束界面，让玩家先看到结算结果
                binding.root.postDelayed({
                    showGameOverDialog()
                }, 2000)
                return
            }
        } else {
            // 应用事件的直接效果（如果有）
            event.effects?.let { effects ->
                Log.d(TAG, "应用事件直接效果: $effects")
                val tempChoice = Choice(
                    text = "事件效果",
                    effects = effects
                )
                GameManager.applyChoice(tempChoice)
            }

            // 设置事件文本
            binding.storyTextView.text = event.text
        }
        Log.d(TAG, "=== 事件文本已更新 ===")
        Log.d(TAG, "事件ID: ${event.id}")
        Log.d(TAG, "事件文本: ${event.text.take(100)}...")
        Log.d(TAG, "事件选择数: ${event.choices.size}")

        // 处理条件事件
        event.conditionalEvents?.let { conditionalEvents ->
            val selectedEventId = evaluateConditionalEvent(conditionalEvents)
            if (selectedEventId != null) {
                Log.d(TAG, "触发条件事件: $selectedEventId")
                // 延迟显示条件事件，让玩家先看到当前事件文本
                binding.root.postDelayed({
                    displayEvent(selectedEventId)
                }, 1000)
                return
            }
        }

        // 处理随机事件
        event.randomChoices?.let { randomChoices ->
            val selectedEventId = GameEventUtils.selectRandomEvent(randomChoices)
            if (selectedEventId != null) {
                Log.d(TAG, "触发随机事件: $selectedEventId")
                // 延迟显示随机事件，让玩家先看到当前事件文本
                binding.root.postDelayed({
                    displayEvent(selectedEventId)
                }, 2000)
                return
            }
        }

        // 特殊调试：对特定事件进行详细分析
        if (eventId == "day1_evening" || eventId.contains("evening")) {
            debugEventChoices(eventId)
        }

        // 获取可用的选择（根据游戏状态过滤）
        currentChoices = getAvailableChoices(event)

        // 特殊处理：如果没有可用选择且没有随机选择或条件事件，创建默认选择
        if (currentChoices.isEmpty() &&
            event.randomChoices.isNullOrEmpty() &&
            event.conditionalEvents.isNullOrEmpty()) {
            Log.w(TAG, "事件 ${event.id} 没有可用选择，创建默认选择")
            currentChoices = listOf(
                Choice(
                    text = "继续",
                    effects = emptyMap(),
                    resultText = "你别无选择，只能继续前进..."
                )
            )
        }

        // 设置选项按钮（只有当有选择时才设置）
        if (currentChoices.isNotEmpty()) {
            setupChoiceButtons(currentChoices)
        } else {
            // 对于没有选择的事件（如随机事件、条件事件），隐藏所有按钮
            binding.choice1Button.visibility = View.GONE
            binding.choice2Button.visibility = View.GONE
            binding.choice3Button.visibility = View.GONE
            Log.d(TAG, "事件 ${event.id} 没有选择，隐藏所有按钮")
        }

        // 更新UI状态显示
        updateUI()

        Log.d(TAG, "事件显示完成，可用选择数: ${currentChoices.size}")
    }

    /**
     * 获取可用的选择
     * 根据游戏状态过滤出玩家可以选择的选项
     */
    private fun getAvailableChoices(event: GameEvent): List<Choice> {
        val gameState = GameManager.gameState
        Log.d(TAG, "检查事件 ${event.id} 的可用选择，总选择数: ${event.choices.size}")
        Log.d(TAG, "当前游戏状态: 体温=${gameState.warmth}, 体力=${gameState.stamina}, 木柴=${gameState.firewood}, 食物=${gameState.food}")

        val availableChoices = event.choices.filter { choice ->
            val isAvailable = GameEventUtils.isChoiceAvailable(choice, gameState)
            Log.d(TAG, "选择 '${choice.text}' 是否可用: $isAvailable, 要求: ${choice.requirements}")

            // 详细记录不可用的原因
            if (!isAvailable && choice.requirements != null) {
                choice.requirements.forEach { (key, value) ->
                    when (key) {
                        "warmth" -> if (gameState.warmth < convertToInt(value)) {
                            Log.d(TAG, "  - 体温不足: 需要${value}, 当前${gameState.warmth}")
                        }
                        "stamina" -> if (gameState.stamina < convertToInt(value)) {
                            Log.d(TAG, "  - 体力不足: 需要${value}, 当前${gameState.stamina}")
                        }
                        "firewood" -> if (gameState.firewood < convertToInt(value)) {
                            Log.d(TAG, "  - 木柴不足: 需要${value}, 当前${gameState.firewood}")
                        }
                        "food" -> if (gameState.food < convertToInt(value)) {
                            Log.d(TAG, "  - 食物不足: 需要${value}, 当前${gameState.food}")
                        }
                    }
                }
            }

            isAvailable
        }

        Log.d(TAG, "可用选择数: ${availableChoices.size}")
        if (availableChoices.isNotEmpty()) {
            availableChoices.forEachIndexed { index, choice ->
                Log.d(TAG, "可用选择 $index: ${choice.text}")
            }
        }

        return availableChoices
    }

    /**
     * 安全地将Any类型转换为Int
     * 处理JSON解析时可能出现的Double类型
     */
    private fun convertToInt(value: Any): Int {
        return when (value) {
            is Int -> value
            is Double -> value.toInt()
            is Float -> value.toInt()
            is Long -> value.toInt()
            is String -> value.toIntOrNull() ?: 0
            else -> 0
        }
    }

    /**
     * 设置选择按钮
     * 根据可用选择和当前UI状态设置按钮的文本和可见性
     */
    private fun setupChoiceButtons(choices: List<Choice>) {
        Log.d(TAG, "设置选择按钮，当前状态: $gameUIState，选择数量: ${choices.size}")

        // 隐藏所有按钮
        binding.choice1Button.visibility = View.GONE
        binding.choice2Button.visibility = View.GONE
        binding.choice3Button.visibility = View.GONE

        when (gameUIState) {
            GameUIState.NORMAL_CHOICE -> {
                // 正常选择状态：显示选择选项
                choices.forEachIndexed { index, choice ->
                    when (index) {
                        0 -> {
                            binding.choice1Button.visibility = View.VISIBLE
                            binding.choice1Button.text = choice.text
                            Log.d(TAG, "设置选择1: ${choice.text}")
                        }
                        1 -> {
                            binding.choice2Button.visibility = View.VISIBLE
                            binding.choice2Button.text = choice.text
                            Log.d(TAG, "设置选择2: ${choice.text}")
                        }
                        2 -> {
                            binding.choice3Button.visibility = View.VISIBLE
                            binding.choice3Button.text = choice.text
                            Log.d(TAG, "设置选择3: ${choice.text}")
                        }
                    }
                }
            }
            GameUIState.WAITING_CONTINUE -> {
                // 等待继续状态：只显示"继续"按钮
                binding.choice1Button.visibility = View.VISIBLE
                binding.choice1Button.text = "继续"
                Log.d(TAG, "设置继续按钮")
            }
            GameUIState.FOOD_CHOICE -> {
                // 食物选择状态：显示食物相关选择
                choices.forEachIndexed { index, choice ->
                    when (index) {
                        0 -> {
                            binding.choice1Button.visibility = View.VISIBLE
                            binding.choice1Button.text = choice.text
                            Log.d(TAG, "设置食物选择1: ${choice.text}")
                        }
                        1 -> {
                            binding.choice2Button.visibility = View.VISIBLE
                            binding.choice2Button.text = choice.text
                            Log.d(TAG, "设置食物选择2: ${choice.text}")
                        }
                    }
                }
            }
            GameUIState.FOOD_WAITING_CONTINUE -> {
                // 食物等待继续状态：只显示"继续"按钮
                binding.choice1Button.visibility = View.VISIBLE
                binding.choice1Button.text = "继续"
                Log.d(TAG, "设置食物继续按钮")
            }
            GameUIState.NIGHT_PHASE -> {
                // 夜晚阶段：隐藏所有按钮
                Log.d(TAG, "夜晚阶段，隐藏所有按钮")
            }
            GameUIState.NIGHT_WAITING_CONTINUE -> {
                // 夜晚等待继续状态：只显示"继续"按钮
                binding.choice1Button.visibility = View.VISIBLE
                binding.choice1Button.text = "继续"
                Log.d(TAG, "设置夜晚继续按钮")
            }
            GameUIState.GAME_OVER -> {
                // 游戏结束：按钮由对话框处理
                Log.d(TAG, "游戏结束状态，按钮由对话框处理")
            }
        }
    }

    /**
     * 显示继续按钮
     * 当显示选择结果后调用此方法
     */
    private fun showContinueButton() {
        if (transitionState("choice_made")) {
            setupChoiceButtons(emptyList()) // 传入空列表，因为此时不需要选择按钮
        }
    }

    /**
     * 设置按钮点击监听器
     * 为三个选项按钮设置 setOnClickListener
     */
    private fun setupClickListeners() {
        // 选择按钮1
        binding.choice1Button.setOnClickListener {
            handleChoiceClick(0)
        }

        // 选择按钮2
        binding.choice2Button.setOnClickListener {
            handleChoiceClick(1)
        }

        // 选择按钮3
        binding.choice3Button.setOnClickListener {
            handleChoiceClick(2)
        }
    }

    /**
     * 处理选择按钮点击
     * 根据当前UI状态处理不同的点击逻辑
     *
     * @param choiceIndex 选择的索引（0, 1, 2）
     */
    private fun handleChoiceClick(choiceIndex: Int) {
        when (gameUIState) {
            GameUIState.NORMAL_CHOICE -> {
                handleNormalChoice(choiceIndex)
            }
            GameUIState.WAITING_CONTINUE -> {
                handleContinueClick()
            }
            GameUIState.FOOD_CHOICE -> {
                handleFoodChoice(choiceIndex)
            }
            GameUIState.FOOD_WAITING_CONTINUE -> {
                handleFoodContinueClick()
            }
            GameUIState.NIGHT_WAITING_CONTINUE -> {
                handleNightContinueClick()
            }
            else -> {
                Log.w(TAG, "当前状态 $gameUIState 不支持按钮点击")
            }
        }
    }

    /**
     * 处理正常选择点击
     * @param choiceIndex 选择的索引
     */
    private fun handleNormalChoice(choiceIndex: Int) {
        if (choiceIndex >= currentChoices.size) {
            Log.w(TAG, "无效的选择索引: $choiceIndex, 可用选择数: ${currentChoices.size}")
            return
        }

        // a. 获取当前事件对应的 Choice 对象
        val choice = currentChoices[choiceIndex]
        Log.d(TAG, "玩家选择: ${choice.text}")

        // b. 调用 GameManager.applyChoice() 来处理数值变化
        val choiceResult = GameManager.applyChoice(choice)

        if (!choiceResult.success) {
            Log.e(TAG, "选择应用失败: ${choiceResult.message}")
            return
        }

        Log.d(TAG, "选择应用成功: ${choiceResult.message}")

        // 显示状态变化信息
        choiceResult.stateChanges.forEach { (property, change) ->
            val (oldValue, newValue) = change
            val diff = newValue - oldValue
            val sign = if (diff >= 0) "+" else ""
            Log.d(TAG, "状态变化 - $property: $oldValue -> $newValue ($sign$diff)")
        }

        // 显示特殊物品获得信息
        choiceResult.specialItemGained?.let { item ->
            Log.d(TAG, "获得特殊物品: $item")
            // 这里可以添加UI提示
        }

        // 更新UI显示
        updateUI()

        // 检查游戏是否结束（选择效果可能导致死亡）
        if (GameManager.checkGameOver()) {
            Log.d(TAG, "选择效果导致游戏结束")
            // 延迟显示游戏结束界面，让玩家先看到选择结果
            binding.root.postDelayed({
                showGameOverDialog()
            }, 2000)
            return
        }

        // 保存当前选择，用于继续时处理
        pendingChoice = choice

        // 显示选择结果文本（如果有）
        choice.resultText?.let { resultText ->
            // 生成状态变化文本
            val stateChangesText = generateStateChangesText(choiceResult.stateChanges)

            // 组合结果文本和状态变化文本
            val fullResultText = if (stateChangesText.isNotEmpty()) {
                "$resultText\n\n$stateChangesText"
            } else {
                resultText
            }

            binding.storyTextView.text = fullResultText
            Log.d(TAG, "显示选择结果: ${resultText.take(50)}...")

            // 显示继续按钮，等待用户点击
            showContinueButton()
        } ?: run {
            // 如果没有结果文本，但有状态变化，显示状态变化
            val stateChangesText = generateStateChangesText(choiceResult.stateChanges)
            if (stateChangesText.isNotEmpty()) {
                binding.storyTextView.text = "你的选择产生了以下效果：\n\n$stateChangesText"
                showContinueButton()
            } else {
                // 如果既没有结果文本也没有状态变化，直接处理下一个事件
                processNextStep(choice)
            }
        }
    }

    /**
     * 处理继续按钮点击
     */
    private fun handleContinueClick() {
        Log.d(TAG, "用户点击继续按钮，当前状态: $gameUIState")

        pendingChoice?.let { choice ->
            Log.d(TAG, "处理待处理的选择: ${choice.text}, nextEventId: ${choice.nextEventId}")

            // 直接处理下一步，不需要状态转换（displayEvent会处理状态）
            processNextStep(choice)
        } ?: run {
            Log.w(TAG, "没有待处理的选择，这可能是一个错误状态")
            // 如果没有待处理的选择，强制回到正常状态
            gameUIState = GameUIState.NORMAL_CHOICE
            setupChoiceButtons(currentChoices)
        }
    }

    /**
     * 处理食物选择按钮点击
     */
    private fun handleFoodChoice(choiceIndex: Int) {
        Log.d(TAG, "用户选择食物选项: $choiceIndex")

        if (choiceIndex < currentChoices.size) {
            val choice = currentChoices[choiceIndex]
            Log.d(TAG, "处理食物选择: ${choice.text}")

            // 记录选择类型
            val isEatingChoice = choice.effects.containsKey("food") && choice.effects["food"] == -1

            // 应用选择效果
            val choiceResult = GameManager.applyChoice(choice)
            val stateChanges = choiceResult.stateChanges

            // 显示选择结果
            val resultText = choice.resultText ?: "你做出了选择。"
            val stateChangesText = generateStateChangesText(stateChanges)
            val fullText = if (stateChangesText.isNotEmpty()) {
                "$resultText\n\n$stateChangesText"
            } else {
                resultText
            }

            binding.storyTextView.text = fullText

            // 更新UI
            updateUI()

            // 保存当前选择信息用于后续判断
            pendingChoice = choice

            // 转换到食物等待继续状态
            if (transitionState("food_choice_made")) {
                setupChoiceButtons(emptyList())
            }
        }
    }

    /**
     * 处理食物继续按钮点击
     */
    private fun handleFoodContinueClick() {
        Log.d(TAG, "用户点击食物继续按钮")

        // 检查上一次的选择是否是进食
        val wasEating = pendingChoice?.effects?.get("food") == -1

        // 清除待处理的选择
        pendingChoice = null

        if (wasEating && GameManager.gameState.food > 0) {
            // 如果上次选择了进食且还有食物，继续显示食物选择
            Log.d(TAG, "继续显示食物选择")
            if (transitionState("food_continue_clicked")) {
                showFoodChoicePhase()
            }
        } else {
            // 如果上次选择了不进食，或者没有食物了，进入夜晚阶段
            Log.d(TAG, "食物阶段结束，进入夜晚阶段")

            // 检查是否有待处理的夜晚事件
            val nightEventId = pendingNightEventId
            pendingNightEventId = null // 清除待处理的夜晚事件ID

            if (nightEventId != null && eventMap.containsKey(nightEventId)) {
                Log.d(TAG, "显示待处理的夜晚事件: $nightEventId")
                if (transitionState("night_phase")) {
                    displayEvent(nightEventId)
                }
            } else {
                Log.d(TAG, "执行夜晚阶段")
                if (transitionState("night_phase")) {
                    performNightPhaseAndCheck()
                }
            }
        }
    }

    /**
     * 处理夜晚继续按钮点击
     */
    private fun handleNightContinueClick() {
        Log.d(TAG, "用户点击夜晚继续按钮")

        // 转换状态并继续游戏流程
        if (transitionState("night_continue_clicked")) {
            // 检查游戏是否结束
            if (GameManager.checkGameOver()) {
                transitionState("game_over")
                showGameOverDialog()
            } else {
                // 进入下一天
                proceedToNextDay()
            }
        }
    }

    /**
     * 生成状态变化文本
     * 使用动态规划方法优化文本生成，只显示可见属性
     * @param stateChanges 状态变化映射
     * @return 格式化的状态变化文本
     */
    private fun generateStateChangesText(stateChanges: Map<String, Pair<Int, Int>>): String {
        if (stateChanges.isEmpty()) return ""

        // 使用动态规划缓存属性名到显示名的映射（只包含可见属性）
        val visiblePropertyDisplayNames = mapOf(
            "warmth" to "体温",
            "stamina" to "体力",
            "firewood" to "木柴",
            "food" to "食物"
        )

        val changes = mutableListOf<String>()

        stateChanges.forEach { (property, change) ->
            // 只处理可见属性
            if (visiblePropertyDisplayNames.containsKey(property)) {
                val (oldValue, newValue) = change
                val diff = newValue - oldValue

                if (diff != 0) {
                    val displayName = visiblePropertyDisplayNames[property]!!
                    val sign = if (diff > 0) "+" else ""
                    changes.add("$displayName$sign$diff")
                }
            }
        }

        return if (changes.isNotEmpty()) {
            "📊 状态变化：${changes.joinToString("，")}"
        } else {
            ""
        }
    }

    /**
     * 处理下一步逻辑
     * 包括加载下一个事件、执行夜晚阶段和检查游戏结束
     */
    private fun processNextStep(choice: Choice) {
        Log.d(TAG, "=== 处理下一步逻辑 ===")
        Log.d(TAG, "选择: ${choice.text}")
        Log.d(TAG, "nextEventId: ${choice.nextEventId}")
        Log.d(TAG, "当前状态: $gameUIState")

        // 清除待处理的选择
        pendingChoice = null

        // c. 如果这个 Choice 有 nextEventId，就调用 displayEvent() 来加载下一个事件
        choice.nextEventId?.let { nextEventId ->
            Log.d(TAG, "准备跳转到下一个事件: $nextEventId")

            // 检查是否是夜晚事件，如果是则先进入食物阶段
            if (nextEventId.contains("night")) {
                Log.d(TAG, "检测到夜晚事件，先检查是否需要食物阶段")
                if (shouldEnterFoodPhase()) {
                    Log.d(TAG, "进入食物阶段")
                    // 保存夜晚事件ID，食物阶段结束后使用
                    pendingNightEventId = nextEventId
                    // 强制转换到食物阶段
                    gameUIState = GameUIState.FOOD_CHOICE
                    showFoodChoicePhase()
                } else {
                    Log.d(TAG, "跳过食物阶段，直接进入夜晚事件")
                    if (eventMap.containsKey(nextEventId)) {
                        displayEvent(nextEventId)
                    } else {
                        // 如果夜晚事件不存在，执行夜晚阶段
                        if (gameUIState == GameUIState.WAITING_CONTINUE && transitionState("night_phase")) {
                            performNightPhaseAndCheck()
                        }
                    }
                }
            } else {
                // 非夜晚事件，正常处理
                if (eventMap.containsKey(nextEventId)) {
                    Log.d(TAG, "显示事件: $nextEventId")
                    displayEvent(nextEventId)
                } else {
                    Log.w(TAG, "事件 $nextEventId 不存在")
                    proceedToNextDay()
                }
            }
        } ?: run {
            Log.d(TAG, "选择没有下一个事件，检查是否需要食物阶段")
            // 检查是否需要进入食物阶段
            if (shouldEnterFoodPhase()) {
                Log.d(TAG, "进入食物阶段")
                // 强制转换到食物阶段
                gameUIState = GameUIState.FOOD_CHOICE
                showFoodChoicePhase()
            } else {
                Log.d(TAG, "直接执行夜晚阶段")
                if (gameUIState == GameUIState.WAITING_CONTINUE && transitionState("night_phase")) {
                    performNightPhaseAndCheck()
                } else {
                    Log.e(TAG, "无法执行夜晚阶段，当前状态: $gameUIState")
                    // 强制执行夜晚阶段
                    gameUIState = GameUIState.WAITING_CONTINUE
                    if (transitionState("night_phase")) {
                        performNightPhaseAndCheck()
                    }
                }
            }
        }

        Log.d(TAG, "=== 下一步逻辑处理完成 ===")
    }

    /**
     * 检查是否应该进入食物阶段
     * 每天夜晚前都会询问玩家是否进食（只要有食物）
     */
    private fun shouldEnterFoodPhase(): Boolean {
        val gameState = GameManager.gameState

        // 只要有食物就进入食物阶段，让玩家选择是否进食
        val hasFood = gameState.food > 0

        Log.d(TAG, "食物阶段检查 - 当前食物:${gameState.food}, 是否进入食物阶段:$hasFood")
        return hasFood
    }

    /**
     * 显示食物选择阶段
     */
    private fun showFoodChoicePhase() {
        Log.d(TAG, "显示食物选择阶段")

        val gameState = GameManager.gameState
        val foodText = "🍖 夜晚来临前\n\n夜幕即将降临，你检查了一下自己的食物储备，还有${gameState.food}份食物。\n\n进食可以恢复体力，帮助你度过寒冷的夜晚。你要进食吗？"

        binding.storyTextView.text = foodText

        // 创建食物选择
        val foodChoices = listOf(
            Choice(
                text = "是，进食（食物-1，体力+40）",
                effects = mapOf("food" to -1, "stamina" to 40),
                resultText = "你打开了一份食物，虽然味道一般，但温热的食物让你感到了一丝慰藉。体力得到了恢复。"
            ),
            Choice(
                text = "否，保存食物",
                effects = emptyMap(),
                resultText = "你决定保存食物，为以后做准备。"
            )
        )

        currentChoices = foodChoices
        setupChoiceButtons(currentChoices)

        Log.d(TAG, "食物选择阶段设置完成")
    }

    /**
     * 执行夜晚阶段并检查游戏结束
     */
    private fun performNightPhaseAndCheck() {
        Log.d(TAG, "执行夜晚阶段...")

        // 执行夜晚阶段
        val nightResult = GameManager.performNightPhase()

        // 更新UI显示
        updateUI()

        // 显示夜晚阶段结果，并转换到等待继续状态
        showNightPhaseResult(nightResult)
    }

    /**
     * 进入下一天
     */
    private fun proceedToNextDay() {
        val currentDay = GameManager.gameState.currentDay
        val nextEventId = "day${currentDay}_start"

        Log.d(TAG, "进入第 $currentDay 天，事件ID: $nextEventId，当前状态: $gameUIState")

        // 确保状态为正常选择状态
        if (gameUIState != GameUIState.NORMAL_CHOICE) {
            gameUIState = GameUIState.NORMAL_CHOICE
            Log.d(TAG, "进入新一天，重置状态为正常选择")
        }

        // 尝试显示下一天的事件
        if (eventMap.containsKey(nextEventId)) {
            displayEvent(nextEventId)
        } else {
            // 如果没有对应的事件，显示通用的新一天开始文本
            val genericText = "第 $currentDay 天\n\n新的一天开始了。你必须继续为生存而努力。"
            binding.storyTextView.text = genericText

            // 显示通用选择
            val genericChoices = listOf(
                Choice(
                    text = "外出寻找资源",
                    effects = mapOf("stamina" to -15, "firewood" to 10, "warmth" to -10)
                ),
                Choice(
                    text = "在屋内休息",
                    effects = mapOf("stamina" to 15)
                )
            )

            currentChoices = genericChoices
            setupChoiceButtons(currentChoices)
        }
    }

    /**
     * 显示夜晚阶段结果
     */
    private fun showNightPhaseResult(nightResult: GameManager.NightPhaseResult) {
        // 生成夜晚状态变化文本
        val stateChanges = mutableMapOf<String, Pair<Int, Int>>()

        // 添加木柴变化
        if (nightResult.firewoodUsed > 0) {
            stateChanges["firewood"] = Pair(nightResult.firewoodBefore, nightResult.firewoodAfter)
        }

        // 添加体温变化
        if (nightResult.warmthChange != 0) {
            stateChanges["warmth"] = Pair(nightResult.warmthBefore, nightResult.warmthAfter)
        }

        val stateChangesText = generateStateChangesText(stateChanges)

        val nightText = if (nightResult.hadEnoughFirewood) {
            "🌙 夜晚降临\n\n壁炉中的火焰温暖地燃烧着，消耗了${nightResult.firewoodUsed}根木柴。你感到体温回升了${nightResult.warmthChange}度。\n\n第${nightResult.dayAfter}天即将到来..."
        } else {
            "🌙 夜晚降临\n\n没有足够的木柴生火，寒冷侵袭着你的身体。你的体温下降了${-nightResult.warmthChange}度。\n\n第${nightResult.dayAfter}天即将到来..."
        }

        // 组合夜晚文本和状态变化文本
        val fullNightText = if (stateChangesText.isNotEmpty()) {
            "$nightText\n\n$stateChangesText"
        } else {
            nightText
        }

        binding.storyTextView.text = fullNightText

        // 转换到夜晚等待继续状态
        if (transitionState("night_shown")) {
            setupChoiceButtons(emptyList()) // 显示继续按钮
        }

        Log.d(TAG, "显示夜晚阶段结果: ${nightText.take(50)}...")
    }

    /**
     * 生成夜晚结算文本
     */
    private fun generateNightSettlementText(nightResult: GameManager.NightPhaseResult): String {
        // 使用IconTextHelper生成无emoji的文本
        val baseText = IconTextHelper.getNightSettlementText(nightResult)

        // 生成状态变化文本
        val stateChanges = mutableMapOf<String, Pair<Int, Int>>()

        // 添加木柴变化
        if (nightResult.firewoodUsed > 0) {
            stateChanges["firewood"] = Pair(nightResult.firewoodBefore, nightResult.firewoodAfter)
        }

        // 添加体温变化
        if (nightResult.warmthChange != 0) {
            stateChanges["warmth"] = Pair(nightResult.warmthBefore, nightResult.warmthAfter)
        }

        // 添加体力变化
        if (nightResult.staminaChange != 0) {
            stateChanges["stamina"] = Pair(nightResult.staminaBefore, nightResult.staminaAfter)
        }

        val stateChangesText = generateStateChangesText(stateChanges)

        return if (stateChangesText.isNotEmpty()) {
            "[月亮] 夜晚结算\n\n$baseText\n\n$stateChangesText"
        } else {
            "[月亮] 夜晚结算\n\n$baseText"
        }
    }

    /**
     * 显示游戏结束对话框
     * 根据 warmth 和 currentDay 的值显示"你胜利了"或"你失败了"，并提供一个"重新开始"的按钮
     */
    private fun showGameOverDialog() {
        val gameState = GameManager.gameState
        val isVictory = GameManager.isVictory()
        val reason = GameManager.getGameOverReason()

        Log.d(TAG, "显示游戏结束对话框: 胜利=$isVictory, 原因=$reason")

        if (isVictory) {
            // 胜利时显示特殊的胜利页面
            showVictoryScreen()
        } else {
            // 失败时显示特殊的失败页面
            showFailureScreen(reason)
        }
    }

    /**
     * 显示特殊的胜利页面
     */
    private fun showVictoryScreen() {
        val gameState = GameManager.gameState

        // 隐藏所有按钮
        binding.choice1Button.visibility = View.GONE
        binding.choice2Button.visibility = View.GONE
        binding.choice3Button.visibility = View.GONE

        // 创建胜利文本
        val signalGunText = if (gameState.specialItems.contains("信号枪")) {
            "\n\n你向天空发射了信号枪，红色的信号弹在雪白的天空中格外醒目。"
        } else {
            ""
        }

        val victoryText = IconTextHelper.getVictoryText(gameState, signalGunText)

        // 设置胜利文本
        binding.storyTextView.text = victoryText

        // 转换到游戏结束状态
        gameUIState = GameUIState.GAME_OVER

        // 延迟3秒后显示选择按钮
        binding.root.postDelayed({
            showVictoryButtons()
        }, 3000)
    }

    /**
     * 显示胜利页面的按钮
     */
    private fun showVictoryButtons() {
        val (button1Text, button2Text) = IconTextHelper.getVictoryButtonTexts()

        binding.choice1Button.visibility = View.VISIBLE
        binding.choice1Button.text = button1Text

        binding.choice2Button.visibility = View.VISIBLE
        binding.choice2Button.text = button2Text

        // 设置按钮点击事件
        binding.choice1Button.setOnClickListener {
            restartGame()
        }

        binding.choice2Button.setOnClickListener {
            finish() // 关闭应用
        }
    }

    /**
     * 显示特殊的失败页面
     */
    private fun showFailureScreen(reason: String) {
        val gameState = GameManager.gameState

        // 隐藏所有按钮
        binding.choice1Button.visibility = View.GONE
        binding.choice2Button.visibility = View.GONE
        binding.choice3Button.visibility = View.GONE

        // 创建失败文本
        val failureText = when (reason) {
            "体温过低" -> IconTextHelper.getFailureTextCold(gameState)
            "体力耗尽" -> IconTextHelper.getFailureTextExhaustion(gameState)
            else -> IconTextHelper.getFailureTextGeneric(gameState, reason)
        }

        // 设置失败文本
        binding.storyTextView.text = failureText

        // 转换到游戏结束状态
        gameUIState = GameUIState.GAME_OVER

        // 延迟3秒后显示选择按钮
        binding.root.postDelayed({
            showFailureButtons()
        }, 3000)
    }

    /**
     * 显示失败页面的按钮
     */
    private fun showFailureButtons() {
        val (button1Text, button2Text) = IconTextHelper.getFailureButtonTexts()

        binding.choice1Button.visibility = View.VISIBLE
        binding.choice1Button.text = button1Text

        binding.choice2Button.visibility = View.VISIBLE
        binding.choice2Button.text = button2Text

        // 设置按钮点击事件
        binding.choice1Button.setOnClickListener {
            restartGame()
        }

        binding.choice2Button.setOnClickListener {
            finish() // 关闭应用
        }
    }

    /**
     * 重新开始游戏
     */
    private fun restartGame() {
        Log.d(TAG, "重新开始游戏")

        // 重置 GameManager
        GameManager.resetGame()

        // 重置UI状态
        gameUIState = GameUIState.NORMAL_CHOICE
        pendingChoice = null

        // 重新开始第一个事件
        displayEvent("day1_start")
    }

    /**
     * 调试方法：分析事件选择问题
     * 专门用于调试选择过滤问题
     */
    private fun debugEventChoices(eventId: String) {
        Log.d(TAG, "=== 调试事件选择: $eventId ===")

        val event = GameEngine.getEvent(eventMap, eventId)
        if (event == null) {
            Log.d(TAG, "事件不存在: $eventId")
            return
        }

        val gameState = GameManager.gameState
        Log.d(TAG, "当前游戏状态:")
        Log.d(TAG, "  体温: ${gameState.warmth}")
        Log.d(TAG, "  体力: ${gameState.stamina}")
        Log.d(TAG, "  木柴: ${gameState.firewood}")
        Log.d(TAG, "  食物: ${gameState.food}")
        Log.d(TAG, "  房屋状况: ${gameState.cabinIntegrity}")
        Log.d(TAG, "  希望值: ${gameState.hope}")

        Log.d(TAG, "事件选择分析:")
        event.choices.forEachIndexed { index, choice ->
            Log.d(TAG, "选择 $index: ${choice.text}")
            Log.d(TAG, "  要求: ${choice.requirements}")
            Log.d(TAG, "  效果: ${choice.effects}")

            val isAvailable = GameEventUtils.isChoiceAvailable(choice, gameState)
            Log.d(TAG, "  是否可用: $isAvailable")

            if (!isAvailable && choice.requirements != null) {
                choice.requirements.forEach { (key, value) ->
                    val required = convertToInt(value)
                    val current = when (key) {
                        "warmth" -> gameState.warmth
                        "stamina" -> gameState.stamina
                        "firewood" -> gameState.firewood
                        "food" -> gameState.food
                        else -> -1
                    }
                    if (current < required) {
                        Log.d(TAG, "    不满足条件: $key 需要$required, 当前$current")
                    }
                }
            }
        }

        Log.d(TAG, "=== 调试结束 ===")
    }

    /**
     * 评估条件事件
     * 根据游戏状态条件选择对应的事件
     * @param conditionalEvents 条件事件映射
     * @return 选中的事件ID，如果没有匹配的条件则返回null
     */
    private fun evaluateConditionalEvent(conditionalEvents: Map<String, String>): String? {
        val gameState = GameManager.gameState

        Log.d(TAG, "评估条件事件，当前状态:")
        Log.d(TAG, "  房屋状况: ${gameState.cabinIntegrity}")
        Log.d(TAG, "  希望值: ${gameState.hope}")
        Log.d(TAG, "  体温: ${gameState.warmth}")
        Log.d(TAG, "  体力: ${gameState.stamina}")

        // 评估每个条件
        conditionalEvents.forEach { (condition, eventId) ->
            if (condition == "default") {
                // 跳过默认条件，最后处理
                return@forEach
            }

            Log.d(TAG, "检查条件: $condition -> $eventId")

            if (evaluateCondition(condition, gameState)) {
                Log.d(TAG, "条件满足: $condition")
                return eventId
            }
        }

        // 如果没有条件满足，返回默认事件
        val defaultEventId = conditionalEvents["default"]
        if (defaultEventId != null) {
            Log.d(TAG, "使用默认事件: $defaultEventId")
        }

        return defaultEventId
    }

    /**
     * 评估单个条件
     * @param condition 条件字符串，如 "cabin_integrity < 50"
     * @param gameState 游戏状态
     * @return 条件是否满足
     */
    private fun evaluateCondition(condition: String, gameState: GameState): Boolean {
        try {
            // 解析条件字符串
            val parts = condition.trim().split(" ")
            if (parts.size != 3) {
                Log.w(TAG, "无效的条件格式: $condition")
                return false
            }

            val property = parts[0]
            val operator = parts[1]
            val value = parts[2].toIntOrNull() ?: return false

            val currentValue = when (property) {
                "cabin_integrity" -> gameState.cabinIntegrity
                "hope" -> gameState.hope
                "warmth" -> gameState.warmth
                "stamina" -> gameState.stamina
                "firewood" -> gameState.firewood
                "food" -> gameState.food
                "currentDay" -> gameState.currentDay
                else -> {
                    Log.w(TAG, "未知的属性: $property")
                    return false
                }
            }

            val result = when (operator) {
                "<" -> currentValue < value
                "<=" -> currentValue <= value
                ">" -> currentValue > value
                ">=" -> currentValue >= value
                "==" -> currentValue == value
                "!=" -> currentValue != value
                else -> {
                    Log.w(TAG, "未知的操作符: $operator")
                    false
                }
            }

            Log.d(TAG, "条件评估: $property($currentValue) $operator $value = $result")
            return result

        } catch (e: Exception) {
            Log.e(TAG, "条件评估错误: $condition", e)
            return false
        }
    }
}