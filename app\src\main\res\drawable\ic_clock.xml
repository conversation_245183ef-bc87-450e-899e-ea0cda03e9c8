<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <!-- 表盘外圈 -->
    <circle
        android:fillColor="#34495E"
        android:cx="12"
        android:cy="12"
        android:radius="10" />
    
    <!-- 表盘内圈 -->
    <circle
        android:fillColor="#ECF0F1"
        android:cx="12"
        android:cy="12"
        android:radius="8" />
    
    <!-- 时刻标记 -->
    <circle android:fillColor="#2C3E50" android:cx="12" android:cy="4" android:radius="0.5" />
    <circle android:fillColor="#2C3E50" android:cx="20" android:cy="12" android:radius="0.5" />
    <circle android:fillColor="#2C3E50" android:cx="12" android:cy="20" android:radius="0.5" />
    <circle android:fillColor="#2C3E50" android:cx="4" android:cy="12" android:radius="0.5" />
    
    <!-- 时针 -->
    <path
        android:fillColor="#E74C3C"
        android:pathData="M12,12L12,8"
        android:strokeWidth="2"
        android:strokeColor="#E74C3C" />
    
    <!-- 分针 -->
    <path
        android:fillColor="#E74C3C"
        android:pathData="M12,12L16,12"
        android:strokeWidth="1.5"
        android:strokeColor="#E74C3C" />
    
    <!-- 中心点 -->
    <circle
        android:fillColor="#E74C3C"
        android:cx="12"
        android:cy="12"
        android:radius="1" />
</vector>
