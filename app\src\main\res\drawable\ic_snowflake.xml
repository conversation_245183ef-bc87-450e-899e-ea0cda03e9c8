<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <!-- 雪花主轴 -->
    <path
        android:fillColor="#E8F4FD"
        android:pathData="M12,2L12,22M2,12L22,12"
        android:strokeWidth="2"
        android:strokeColor="#E8F4FD" />
    
    <!-- 雪花对角线 -->
    <path
        android:fillColor="#E8F4FD"
        android:pathData="M5.5,5.5L18.5,18.5M18.5,5.5L5.5,18.5"
        android:strokeWidth="1.5"
        android:strokeColor="#E8F4FD" />
    
    <!-- 雪花分支 -->
    <path
        android:fillColor="#E8F4FD"
        android:pathData="M12,4L10,6M12,4L14,6M12,20L10,18M12,20L14,18"
        android:strokeWidth="1"
        android:strokeColor="#E8F4FD" />
    
    <path
        android:fillColor="#E8F4FD"
        android:pathData="M4,12L6,10M4,12L6,14M20,12L18,10M20,12L18,14"
        android:strokeWidth="1"
        android:strokeColor="#E8F4FD" />
    
    <path
        android:fillColor="#E8F4FD"
        android:pathData="M7,7L8,6M7,7L6,8M17,17L18,16M17,17L16,18"
        android:strokeWidth="1"
        android:strokeColor="#E8F4FD" />
    
    <path
        android:fillColor="#E8F4FD"
        android:pathData="M17,7L16,6M17,7L18,8M7,17L6,16M7,17L8,18"
        android:strokeWidth="1"
        android:strokeColor="#E8F4FD" />
    
    <!-- 中心点 -->
    <circle
        android:fillColor="#FFFFFF"
        android:cx="12"
        android:cy="12"
        android:radius="1.5" />
</vector>
