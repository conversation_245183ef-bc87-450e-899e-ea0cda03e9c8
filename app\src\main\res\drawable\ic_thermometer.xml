<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    
    <!-- 温度计主体 -->
    <rect
        android:fillColor="#ECF0F1"
        android:x="10"
        android:y="3"
        android:width="4"
        android:height="14"
        android:rx="2" />
    
    <!-- 温度计底部球体 -->
    <circle
        android:fillColor="#E74C3C"
        android:cx="12"
        android:cy="18"
        android:radius="3" />
    
    <!-- 温度计内部管道 -->
    <rect
        android:fillColor="#BDC3C7"
        android:x="11"
        android:y="4"
        android:width="2"
        android:height="12"
        android:rx="1" />
    
    <!-- 温度液体 -->
    <rect
        android:fillColor="#E74C3C"
        android:x="11"
        android:y="10"
        android:width="2"
        android:height="6"
        android:rx="1" />
    
    <!-- 刻度线 -->
    <path
        android:fillColor="#2C3E50"
        android:pathData="M14,5L15,5M14,7L15,7M14,9L15,9M14,11L15,11M14,13L15,13M14,15L15,15"
        android:strokeWidth="0.5"
        android:strokeColor="#2C3E50" />
    
    <!-- 温度计顶部 -->
    <rect
        android:fillColor="#95A5A6"
        android:x="10"
        android:y="3"
        android:width="4"
        android:height="1"
        android:rx="2" />
</vector>
