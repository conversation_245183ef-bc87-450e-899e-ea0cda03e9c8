<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="#1a1a1a"
    android:padding="32dp">

    <!-- 游戏标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="❄️ 雪山求生 ❄️"
        android:textSize="36sp"
        android:textColor="#ffffff"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <!-- 副标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Mountain Survival"
        android:textSize="18sp"
        android:textColor="#cccccc"
        android:layout_marginBottom="48dp"
        android:gravity="center" />

    <!-- 游戏描述 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="🏔️ 你被困在雪山中的一间小屋里\n🔥 管理你的体温、体力和资源\n⏰ 坚持7天等待救援\n💀 体温或体力归零即死亡\n\n你能在严酷的雪山中生存下来吗？"
        android:textSize="16sp"
        android:textColor="#e0e0e0"
        android:lineSpacingExtra="4dp"
        android:gravity="center"
        android:layout_marginBottom="64dp" />

    <!-- 开始游戏按钮 -->
    <Button
        android:id="@+id/startGameButton"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:text="🎮 开始游戏"
        android:textSize="18sp"
        android:textColor="#ffffff"
        android:background="@drawable/button_background"
        android:layout_marginBottom="16dp" />

    <!-- 退出游戏按钮 -->
    <Button
        android:id="@+id/exitGameButton"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:text="🚪 退出游戏"
        android:textSize="18sp"
        android:textColor="#ffffff"
        android:background="@drawable/button_background_secondary"
        android:layout_marginBottom="32dp" />

    <!-- 版本信息 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="版本 1.0.0"
        android:textSize="12sp"
        android:textColor="#888888"
        android:layout_marginTop="32dp" />

</LinearLayout>
