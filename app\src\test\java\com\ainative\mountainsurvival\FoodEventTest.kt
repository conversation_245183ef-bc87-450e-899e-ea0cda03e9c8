package com.ainative.mountainsurvival

import org.junit.Test
import org.junit.Assert.*
import org.junit.Before

/**
 * 食物事件功能测试
 * 测试夜晚前的食物选择功能
 */
class FoodEventTest {

    @Before
    fun setUp() {
        // 初始化游戏状态
        GameManager.initializeGame()
    }

    @Test
    fun testShouldEnterFoodPhase_withFood() {
        // 测试有食物时应该进入食物阶段
        
        // 设置游戏状态：有食物，体力较低
        GameManager.gameState.food = 2
        GameManager.gameState.stamina = 50
        GameManager.gameState.currentDay = 3
        
        // 模拟 shouldEnterFoodPhase 逻辑
        val remainingDays = 7 - GameManager.gameState.currentDay + 1
        val currentStamina = GameManager.gameState.stamina
        val currentFood = GameManager.gameState.food
        
        // 简化版本：如果体力低于60且有食物，建议进食
        val shouldEat = currentStamina < 60 || (currentFood > remainingDays)
        
        assertTrue("有食物且体力低时应该进入食物阶段", shouldEat)
    }

    @Test
    fun testShouldEnterFoodPhase_noFood() {
        // 测试没有食物时不应该进入食物阶段
        
        // 设置游戏状态：没有食物
        GameManager.gameState.food = 0
        GameManager.gameState.stamina = 50
        GameManager.gameState.currentDay = 3
        
        // 没有食物时应该返回false
        val shouldEat = GameManager.gameState.food > 0
        
        assertFalse("没有食物时不应该进入食物阶段", shouldEat)
    }

    @Test
    fun testFoodChoice_eating() {
        // 测试进食选择
        
        // 设置初始状态
        val initialFood = 3
        val initialStamina = 60
        GameManager.gameState.food = initialFood
        GameManager.gameState.stamina = initialStamina
        
        // 创建进食选择
        val eatChoice = Choice(
            text = "是，进食（食物-1，体力+40）",
            effects = mapOf("food" to -1, "stamina" to 40),
            resultText = "你打开了一份食物，虽然味道一般，但温热的食物让你感到了一丝慰藉。体力得到了恢复。"
        )
        
        // 应用选择
        val result = GameManager.applyChoice(eatChoice)
        
        // 验证结果
        assertTrue("进食选择应该成功", result.success)
        assertEquals("食物应该减少1", initialFood - 1, GameManager.gameState.food)
        assertEquals("体力应该增加40", (initialStamina + 40).coerceAtMost(100), GameManager.gameState.stamina)
        assertTrue("状态变化应该包含食物变化", result.stateChanges.containsKey("food"))
        assertTrue("状态变化应该包含体力变化", result.stateChanges.containsKey("stamina"))
    }

    @Test
    fun testFoodChoice_notEating() {
        // 测试不进食选择
        
        // 设置初始状态
        val initialFood = 3
        val initialStamina = 60
        GameManager.gameState.food = initialFood
        GameManager.gameState.stamina = initialStamina
        
        // 创建不进食选择
        val noEatChoice = Choice(
            text = "否，保存食物",
            effects = emptyMap(),
            resultText = "你决定保存食物，为以后做准备。"
        )
        
        // 应用选择
        val result = GameManager.applyChoice(noEatChoice)
        
        // 验证结果
        assertTrue("不进食选择应该成功", result.success)
        assertEquals("食物应该保持不变", initialFood, GameManager.gameState.food)
        assertEquals("体力应该保持不变", initialStamina, GameManager.gameState.stamina)
        assertTrue("状态变化应该为空", result.stateChanges.isEmpty())
    }

    @Test
    fun testFoodChoice_multipleEating() {
        // 测试多次进食
        
        // 设置初始状态
        val initialFood = 5
        val initialStamina = 20
        GameManager.gameState.food = initialFood
        GameManager.gameState.stamina = initialStamina
        
        // 第一次进食
        val eatChoice = Choice(
            text = "是，进食（食物-1，体力+40）",
            effects = mapOf("food" to -1, "stamina" to 40),
            resultText = "你打开了一份食物，虽然味道一般，但温热的食物让你感到了一丝慰藉。体力得到了恢复。"
        )
        
        GameManager.applyChoice(eatChoice)
        
        // 验证第一次进食后的状态
        assertEquals("第一次进食后食物应该减少1", initialFood - 1, GameManager.gameState.food)
        assertEquals("第一次进食后体力应该增加40", initialStamina + 40, GameManager.gameState.stamina)
        
        // 第二次进食
        val currentFood = GameManager.gameState.food
        val currentStamina = GameManager.gameState.stamina
        
        GameManager.applyChoice(eatChoice)
        
        // 验证第二次进食后的状态
        assertEquals("第二次进食后食物应该再减少1", currentFood - 1, GameManager.gameState.food)
        assertEquals("第二次进食后体力应该再增加40（但不超过100）", 
            (currentStamina + 40).coerceAtMost(100), GameManager.gameState.stamina)
    }

    @Test
    fun testFoodChoice_staminaLimit() {
        // 测试体力上限
        
        // 设置初始状态：体力接近上限
        GameManager.gameState.food = 3
        GameManager.gameState.stamina = 90
        
        // 进食选择
        val eatChoice = Choice(
            text = "是，进食（食物-1，体力+40）",
            effects = mapOf("food" to -1, "stamina" to 40),
            resultText = "你打开了一份食物，虽然味道一般，但温热的食物让你感到了一丝慰藉。体力得到了恢复。"
        )
        
        GameManager.applyChoice(eatChoice)
        
        // 验证体力不会超过100
        assertEquals("体力不应该超过100", 100, GameManager.gameState.stamina)
    }

    @Test
    fun testDynamicProgrammingDecision() {
        // 测试动态规划决策算法
        
        // 测试场景1：体力低，食物多，应该进食
        GameManager.gameState.food = 5
        GameManager.gameState.stamina = 30
        GameManager.gameState.currentDay = 2
        
        val remainingDays1 = 7 - GameManager.gameState.currentDay + 1
        val shouldEat1 = GameManager.gameState.stamina < 60 || (GameManager.gameState.food > remainingDays1)
        
        assertTrue("体力低且食物充足时应该进食", shouldEat1)
        
        // 测试场景2：体力高，食物少，不应该进食
        GameManager.gameState.food = 1
        GameManager.gameState.stamina = 80
        GameManager.gameState.currentDay = 5
        
        val remainingDays2 = 7 - GameManager.gameState.currentDay + 1
        val shouldEat2 = GameManager.gameState.stamina < 60 || (GameManager.gameState.food > remainingDays2)
        
        assertFalse("体力高且食物不足时不应该进食", shouldEat2)
    }
}
