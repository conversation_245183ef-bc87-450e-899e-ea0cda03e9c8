package com.ainative.mountainsurvival

import org.junit.Test
import org.junit.Assert.*

/**
 * UI状态管理测试
 * 测试动态规划状态转换和数值变化显示功能
 */
class UIStateTest {

    @Test
    fun testStateTransitions() {
        // 测试状态转换逻辑
        val transitions = mapOf(
            Pair(MainActivity.GameUIState.NORMAL_CHOICE, "choice_made") to MainActivity.GameUIState.WAITING_CONTINUE,
            Pair(MainActivity.GameUIState.WAITING_CONTINUE, "continue_clicked") to MainActivity.GameUIState.NORMAL_CHOICE,
            Pair(MainActivity.GameUIState.WAITING_CONTINUE, "night_phase") to MainActivity.GameUIState.NIGHT_PHASE,
            Pair(MainActivity.GameUIState.NIGHT_PHASE, "night_shown") to MainActivity.GameUIState.NIGHT_WAITING_CONTINUE,
            Pair(MainActivity.GameUIState.NIGHT_WAITING_CONTINUE, "night_continue_clicked") to MainActivity.GameUIState.NORMAL_CHOICE
        )
        
        // 验证所有转换都是有效的
        transitions.forEach { (from, to) ->
            assertNotNull("状态转换应该有效: ${from.first} + ${from.second} -> $to", to)
        }
    }

    @Test
    fun testStateChangesTextGeneration() {
        // 模拟状态变化数据
        val stateChanges = mapOf(
            "warmth" to Pair(80, 70),    // 体温-10
            "stamina" to Pair(50, 65),   // 体力+15
            "firewood" to Pair(5, 3),    // 木柴-2
            "food" to Pair(3, 2)         // 食物-1
        )
        
        // 验证生成的文本包含正确的变化信息
        val expectedChanges = listOf("体温-10", "体力+15", "木柴-2", "食物-1")
        
        // 这里我们只能测试逻辑，因为实际的文本生成在MainActivity中
        stateChanges.forEach { (property, change) ->
            val (oldValue, newValue) = change
            val diff = newValue - oldValue
            val sign = if (diff > 0) "+" else ""
            
            when (property) {
                "warmth" -> assertEquals("体温变化应该正确", -10, diff)
                "stamina" -> assertEquals("体力变化应该正确", 15, diff)
                "firewood" -> assertEquals("木柴变化应该正确", -2, diff)
                "food" -> assertEquals("食物变化应该正确", -1, diff)
            }
        }
    }

    @Test
    fun testGameUIStateEnum() {
        // 测试枚举值
        val states = MainActivity.GameUIState.values()
        
        assertTrue("应该包含NORMAL_CHOICE状态", states.contains(MainActivity.GameUIState.NORMAL_CHOICE))
        assertTrue("应该包含WAITING_CONTINUE状态", states.contains(MainActivity.GameUIState.WAITING_CONTINUE))
        assertTrue("应该包含NIGHT_PHASE状态", states.contains(MainActivity.GameUIState.NIGHT_PHASE))
        assertTrue("应该包含NIGHT_WAITING_CONTINUE状态", states.contains(MainActivity.GameUIState.NIGHT_WAITING_CONTINUE))
        assertTrue("应该包含GAME_OVER状态", states.contains(MainActivity.GameUIState.GAME_OVER))
        
        assertEquals("应该有5个状态", 5, states.size)
    }
}
