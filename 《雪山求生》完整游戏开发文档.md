# **《雪山求生》完整游戏开发文档 (GDD)**

### **1\. 游戏概述 (Game Overview)**

* **游戏名称:** 雪山求生 (Mountain Survival)  
* **游戏类型:** 文字生存模拟游戏  
* **平台:** 安卓 (Android)  
* **核心概念:** 玩家扮演一名被暴风雪困在山顶小屋的登山者，必须在7天内合理分配体力、管理资源、维持体温，做出艰难的抉择，直到风雪停止，迎来救援。  
* **目标体验:** 强烈的代入感、孤独感、资源紧缺的压迫感，以及成功存活后的巨大成就感。

### **2\. 核心玩法机制 (Core Gameplay Mechanics)**

* **游戏目标:** 存活7天。  
* **游戏循环 (Game Loop):** 游戏以“天”为单位进行。  
  1. **清晨阶段 (Morning Phase):**  
     * 显示天数（例如 "第 3 天"）。  
     * 显示所有核心数值的当前状态。  
     * 呈现当天的主要事件或行动选项。  
  2. **决策阶段 (Decision Phase):**  
     * 玩家从2-3个选项中选择一个执行。  
     * 选择后，立即结算该选项带来的数值变化。  
  3. **黄昏阶段 (Evening Phase):**  
     * 显示一段描述黄昏景色的文本。  
     * 如果玩家选择，可以执行“进食”或“加固房屋”等特殊行动。  
  4. **夜晚阶段 (Night Phase):**  
     * 自动结算：消耗木柴生火，并根据壁炉状态结算体温变化。  
     * 检查是否触发游戏结束条件。  
     * 若未结束，天数+1，进入新的一天。

### **3\. 游戏数值设计 (Game Stats Design)**

#### **核心数值 (Visible Stats):**

| 数值名称 | 初始值 | 最小值 | 最大值 | 描述与影响 |
| :---- | :---- | :---- | :---- | :---- |
| **体温 (Warmth)** | 80 | 0 | 100 | **生命线。** 主要通过壁炉维持。外出、天黑都会降低。**降至0则游戏失败。** |
| **体力 (Stamina)** | 100 | 0 | 100 | **行动力。** 执行大部分行动都需要消耗。可通过休息、进食恢复。**降至0无法行动，可能导致失败。** |
| **木柴 (Firewood)** | 5 | 0 | 50 | **热量来源。** 夜晚壁炉会自动消耗。主要通过外出砍柴获得。 |
| **食物 (Food)** | 3 | 0 | 20 | **体力来源。** 可在黄昏阶段选择“进食”来恢复大量体力。 |

#### **隐藏数值 (Hidden Stats):**

| 数值名称 | 初始值 | 描述与影响 |
| :---- | :---- | :---- |
| **房屋状况 (Cabin Integrity)** | 100 | 代表小屋的坚固程度。某些事件（如暴风雪加剧）会降低它，玩家需要消耗资源修复。过低会触发负面事件。 |
| **希望值 (Hope)** | 50 | 影响某些事件的文本描述和结局的文本。做出积极的选择会增加，反之则减少。 |

#### **数值结算规则:**

* **夜晚结算:**  
  * 如果木柴 \>= 5: 木柴 \-5, 体温 \+10 (上限100)。  
  * 如果木柴 \<5: 体温 \-20。  
* **进食结算:**  
  * 食物 \-1, 体力 \+40 (上限100)。

### **4\. 游戏内容与事件 (Game Content & Events)**

这是游戏的核心。所有事件都将以JSON格式存储。

#### **第一天 (Day 1: The Shelter)**

* **清晨文本:** "刺骨的寒风把你从昏迷中唤醒。你挣扎着睁开眼，发现自己正躺在一间破旧的小木屋里。屋外，暴风雪的呼啸声如同怪物的嘶吼。你检查了一下自己的状况，必须立刻想办法生火！"  
* **主要行动选项:**  
  1. **【劈开旧家具生火】**  
     * **效果:** 体力 \-10, 木柴 \+10, 房屋状况 \-5。  
     * **后续文本:** "你用墙角的斧头劈开了一把摇摇欲坠的椅子，冰冷的木屋里终于有了一丝暖意。"  
  2. **【冒着风雪出去找柴火】**  
     * **效果:** 体温 \-20, 体力 \-20, 木柴 \+15。  
     * **后续文本:** "你推开门，几乎被风雪掀翻。你在及膝深的雪地里艰难地收集了一些干树枝，冻僵的手指已经失去了知觉。"

#### **第二天 (Day 2: The First Morning)**

* **清晨文本:** "火焰在壁炉里噼啪作响，你活过了第一个夜晚。但你知道，这仅仅是开始。你必须为接下来的日子做准备。"  
* **主要行动选项:**  
  1. **【探索小屋】**  
     * **效果:** 体力 \-10。  
     * **后续事件触发:** 70%概率触发 "发现旧罐头"，30%概率触发 "发现破旧的日记"。  
  2. **【外出砍柴】**  
     * **效果:** 体温 \-15, 体力 \-20, 木柴 \+15。  
  3. **【休息以保存体力】**  
     * **效果:** 体力 \+20。  
* **后续事件 \- 发现旧罐头:**  
  * **文本:** "在一个布满灰尘的床下，你发现了一罐看不清标签的罐头。"  
  * **选项:**  
    * **【打开它】:** 食物 \+3, 体力 \+10。  
    * **【先留着】:** 无效果。  
* **后续事件 \- 发现破旧的日记:**  
  * **文本:** "你找到了一本破旧的日记，字迹已经模糊不清，但你依稀能辨认出一些文字：‘...雪越来越大了...食物...它在外面...’"  
  * **选项:**  
    * **【仔细阅读】:** 体力 \-5, 希望值 \+10。  
    * **【扔进火里】:** 木柴 \+1, 希望值 \-5。

#### **第三天 (Day 3: The Howling Wind)**

* **清晨文本:** "风声变得更加尖锐，仿佛要撕裂这间小屋。你感到一阵不安，屋顶的积雪似乎太厚了。"  
* **主要行动选项:**  
  1. **【加固小屋】**  
     * **效果:** 体力 \-15, 木柴 \-5, 房屋状况 \+20。  
  2. **【尝试捕猎】**  
     * **效果:** 体温 \-20, 体力 \-30。50%概率 食物 \+5，50%概率一无所获。  
  3. **【继续休息】**  
     * **效果:** 体力 \+20。

#### **第四天 (Day 4: A Glimmer of Hope)**

* **清晨文本:** "风雪似乎短暂地减弱了。透过窗户的缝隙，你似乎看到了远处有什么东西在反光。"  
* **主要行动选项:**  
  1. **【前往反光点调查】**  
     * **效果:** 体温 \-25, 体力 \-30。  
     * **后续事件触发:** "坠毁的飞机"。  
  2. **【这是陷阱，待在屋里】**  
     * **效果:** 希望值 \-10。  
* **后续事件 \- 坠毁的飞机:**  
  * **文本:** "你深一脚浅一脚地跋涉过去，发现那是一架小型飞机的残骸！机舱里一片狼藉。"  
  * **选项:**  
    * **【搜索驾驶舱】:** 获得一个 "信号枪" (特殊物品，影响结局)。  
    * **【搜索货仓】:** 食物 \+10。

#### **第五天 (Day 5: Despair)**

* **清晨文本:** "风雪再次加强，比以往任何时候都要猛烈。小屋在狂风中颤抖，你感到一阵深深的绝望。"  
* **主要行动选项:**  
  1. **【检查房屋状况】**  
     * **效果:** 体力 \-10。如果房屋状况 \< 50，触发 "屋顶漏雪" 事件。  
  2. **【蜷缩在壁炉边】**  
     * **效果:** 体力 \+10, 希望值 \-5。  
* **后续事件 \- 屋顶漏雪:**  
  * **文本:** "屋顶的一角开始漏下冰冷的雪水，很快就会让你的体温迅速流失！"  
  * **选项:**  
    * **【立刻修复】:** 体力 \-20, 木柴 \-10, 房屋状况 \+15。  
    * **【暂时不管】:** 每晚体温额外 \-5。

#### **第六天 (Day 6: The Long Night)**

* **清晨文本:** "这可能是最后一个夜晚了，也可能是你人生的最后一个夜晚。你必须撑过去。"  
* **主要行动选项:**  
  1. **【把所有能烧的都烧了】**  
     * **效果:** 木柴 \+20, 房屋状况 \-20, 希望值 \+10。  
  2. **【做最后的准备】**  
     * **效果:** 体力 \+10。

#### **第七天 (Day 7: The Final Stand)**

* **清晨文本:** "你几乎已经麻木了。生存的本能驱使着你睁开眼睛。窗外依旧是白茫茫的一片。"  
* **主要行动选项:**  
  1. **【等待】**  
     * **效果:** 无。  
  2. **【如果有信号枪，向天空发射】**  
     * **效果:** 消耗 "信号枪"，希望值 \+50。

### **5\. 游戏流程逻辑 (Game Flow Logic)**

1. **主菜单 (MainMenu):**  
   * 显示游戏标题《雪山求生》。  
   * 一个【开始游戏】按钮。  
2. **游戏界面 (GameActivity):**  
   * 点击【开始游戏】后，初始化所有数值，加载第一天的事件。  
   * GameLoop 开始运行。  
3. **游戏结束 (Game Over):**  
   * **触发条件:**  
     * 体温 \<= 0。  
     * 存活超过7天。  
   * **跳转至结束界面 (EndScreen):**  
     * 显示结局文本。  
       * **胜利结局:** "第8天清晨，风雪停了。一缕久违的阳光照在你的脸上。远处传来了救援直升机的轰鸣声...你活下来了。" (如果使用了信号枪，文本会更积极)。  
       * **失败结局:** "你的意识渐渐模糊，壁炉的火焰在你眼中变成了遥远的星辰。你再也没有醒来。"  
     * 一个【重新开始】按钮，点击后返回主菜单。

### **6\. 用户界面与体验 (UI/UX Design)**

* **布局 (Layout):**  
  * **顶部 (20%):** 体温 体力 木柴 食物 四个图标+数值并排。  
  * **中部 (50%):** 大块的TextView，用于显示事件文本。  
  * **底部 (30%):** 2-3个Button，用于显示选项。  
* **视觉风格 (Visual Style):**  
  * **背景:** 一张固定的、透过结霜的窗户看向屋外暴风雪的图片。  
  * **字体:** 使用一种带有手写感的、略显严肃的字体。  
  * **配色:** 主色调为深蓝(\#2C3E50)、灰色(\#95A5A6)、白色(\#ECF0F1)。体温数值使用暖橙色(\#E67E22)突出显示。  
* **音效 (Sound \- 进阶项):**  
  * **背景音:** 持续的、低沉的风雪呼啸声。  
  * **事件音:** 壁炉燃烧的噼啪声、点击按钮的厚重音效、砍柴的音效。

### **7\. 技术实现方案 (Technical Implementation Plan)**

* **项目结构:**  
  * MainActivity.kt: 游戏主活动。  
  * GameManager.kt: 单例，管理所有游戏状态和数值。  
  * Event.kt: 数据类，定义事件的数据结构。  
  * EventEngine.kt: 负责从JSON加载和提供事件。  
  * assets/events.json: 存储所有游戏内容的JSON文件。  
* **JSON Schema (events.json):**  
  {  
    "events": \[  
      {  
        "id": "day1\_start",  
        "text": "刺骨的寒风把你从昏迷中唤醒...",  
        "choices": \[  
          {  
            "text": "劈开旧家具生火",  
            "effects": { "stamina": \-10, "firewood": 10, "cabin\_integrity": \-5 },  
            "result\_text": "你用墙角的斧头劈开了一把摇摇欲坠的椅子..."  
          },  
          {  
            "text": "冒着风雪出去找柴火",  
            "effects": { "warmth": \-20, "stamina": \-20, "firewood": 15 },  
            "result\_text": "你推开门，几乎被风雪掀翻..."  
          }  
        \]  
      },  
      {  
        "id": "day2\_explore",  
        "text": "你决定探索一下这间小屋...",  
        "effects": { "stamina": \-10 },  
        "random\_choices": \[  
          { "chance": 0.7, "next\_event\_id": "found\_cans" },  
          { "chance": 0.3, "next\_event\_id": "found\_diary" }  
        \]  
      }  
    \]  
  }

  * id: 事件的唯一标识符。  
  * text: 显示给玩家的描述文本。  
  * choices: 玩家的选项列表。  
    * text: 按钮上显示的文本。  
    * effects: 点击后对各项数值的影响。  
    * result\_text: (可选) 点击后显示的反馈文本。  
    * next\_event\_id: (可选) 点击后触发的下一个事件ID。  
  * random\_choices: (可选) 用于实现随机事件，系统会自动根据chance概率选择一个next\_event\_id。