# 《雪山求生》白天夜晚事件拆分完成说明

## 修改概述

我已经成功将白天事件和夜晚事件拆分，现在游戏严格按照以下流程进行：

**白天事件 ➡ 食物事件 ➡ 夜晚事件 ➡ 第二天**

## 具体修改内容

### 1. 事件结构修改

#### 第二天事件拆分
- **修改前**：day2_start 的选择直接跳转到 day3_start，resultText 包含夜晚描述
- **修改后**：
  - day2_start 的选择跳转到 day2_night
  - 移除 resultText 中的夜晚描述
  - 新增独立的 day2_night 事件

#### 第三天事件拆分
- **修改前**：day3_start 的选择直接跳转到 day4_start，resultText 包含夜晚描述
- **修改后**：
  - day3_start 的选择跳转到 day3_night
  - 移除 resultText 中的夜晚描述
  - 新增独立的 day3_night 事件

#### 第四天事件拆分
- **修改前**：day4_start 的选择直接跳转到 day5_start，resultText 包含夜晚描述
- **修改后**：
  - day4_start 的选择跳转到 day4_night
  - 移除 resultText 中的夜晚描述
  - 新增独立的 day4_night 事件

#### 第五天事件拆分
- **修改前**：day5_start 相关事件直接跳转到 day6_start，resultText 包含夜晚描述
- **修改后**：
  - 所有 day5 相关事件跳转到 day5_night
  - 移除 resultText 中的夜晚描述
  - day5_night 事件已存在，确保正确跳转

### 2. 新增的夜晚事件

#### day2_night
```json
{
  "id": "day2_night",
  "text": "第二个夜晚来临了。你已经更加熟悉这间小屋，也更加了解如何在这里生存。壁炉中的火焰依然温暖，但你知道每一夜都是对生存意志的考验。",
  "effects": {
    "currentDay": 1
  },
  "choices": [
    {
      "text": "继续下一天",
      "effects": {},
      "resultText": "又一个夜晚过去了，你离救援又近了一步...",
      "nextEventId": "day3_start"
    }
  ]
}
```

#### day3_night
```json
{
  "id": "day3_night",
  "text": "第三个夜晚来临了。风声更加猛烈，小屋在狂风中摇摆。你紧紧抱着自己，希望能撑过这一夜。外面的暴风雪似乎永远不会停止。",
  "effects": {
    "currentDay": 1
  },
  "choices": [
    {
      "text": "继续下一天",
      "effects": {},
      "resultText": "第三夜过去了，你依然活着...",
      "nextEventId": "day4_start"
    }
  ]
}
```

#### day4_night
```json
{
  "id": "day4_night",
  "text": "第四个夜晚。今天的发现让你对明天充满了期待，但现在你必须先撑过这一夜。你握着手中的信号枪，这给了你一丝希望。",
  "effects": {
    "currentDay": 1
  },
  "choices": [
    {
      "text": "继续下一天",
      "effects": {},
      "resultText": "第四夜过去了，你离救援更近了...",
      "nextEventId": "day5_start"
    }
  ]
}
```

### 3. 修改的具体事件

#### day2_start 相关事件
- "探索小屋寻找物资" → nextEventId: "day2_night"
- "外出砍柴" → nextEventId: "day2_night"  
- "休息以保存体力" → nextEventId: "day2_night"

#### day3_start 相关事件
- "加固小屋" → nextEventId: "day3_night"
- "尝试捕猎" → nextEventId: "day3_night"
- "继续休息" → nextEventId: "day3_night"

#### day4_start 相关事件
- "前往反光点调查" → nextEventId: "day4_night"
- "这是陷阱，待在屋里" → nextEventId: "day4_night"

#### day5_start 相关事件
- "蜷缩在壁炉边" → nextEventId: "day5_night"
- "roof_leak" 事件的两个选择 → nextEventId: "day5_night"
- "house_ok" 事件 → nextEventId: "day5_night"

## 游戏流程验证

现在游戏流程为：

1. **第一天**：day1_start → day1_evening → day1_night → day2_start
2. **第二天**：day2_start → [食物事件] → day2_night → day3_start
3. **第三天**：day3_start → [食物事件] → day3_night → day4_start
4. **第四天**：day4_start → [食物事件] → day4_night → day5_start
5. **第五天**：day5_start → [食物事件] → day5_night → day6_start
6. **第六天及以后**：保持原有结构

## 食物事件集成

食物事件会在以下时机触发：
- 当白天事件要跳转到夜晚事件时（如 day2_night, day3_night 等）
- 系统会自动拦截这些跳转，先进入食物选择阶段
- 食物阶段结束后，恢复并执行原定的夜晚事件

## 技术实现

1. **MainActivity.kt** 中的 `processNextStep()` 方法会检测夜晚事件
2. 使用 `pendingNightEventId` 保存原定的夜晚事件ID
3. 食物阶段结束后恢复并执行夜晚事件
4. 确保事件流程的完整性和一致性

## 验证结果

- ✅ 白天事件和夜晚事件已完全拆分
- ✅ 每天都有独立的夜晚事件
- ✅ 食物事件正确插入在白天和夜晚之间
- ✅ 事件流程保持完整性
- ✅ 编译通过，无语法错误

现在游戏严格按照您要求的顺序：**白天事件 ➡ 食物事件 ➡ 夜晚事件 ➡ 第二天**
